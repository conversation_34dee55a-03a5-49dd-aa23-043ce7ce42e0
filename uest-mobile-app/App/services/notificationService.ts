import AsyncStorage from '@react-native-async-storage/async-storage';
import {apiUrl} from '../config/apiUrl';

export interface Notification {
  id: string;
  userId: string;
  userType: 'STUDENT' | 'CLASS' | 'ADMIN';
  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |
        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |
        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |
        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |
        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |
        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |
        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationPagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface NotificationResponse {
  notifications: Notification[];
  pagination: NotificationPagination;
}

const getCommonHeaders = async () => {
  const token = await AsyncStorage.getItem('token');
  return {
    'Content-Type': 'application/json',
    Accept: '*/*',
    'client-type': 'uest-mobile-app',
    Cookie: token ? `student_jwt=${token}` : '',
  };
};

// Get student notifications
export const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {
  try {
    const headers = await getCommonHeaders();
    const url = `${apiUrl}/notifications/students?page=${page}&limit=${limit}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching student notifications:', error);
    throw error;
  }
};

// Get unread notification count for students
export const getStudentUnreadCount = async (): Promise<number> => {
  try {
    const headers = await getCommonHeaders();
    const url = `${apiUrl}/notifications/students/count`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result.data.count;
  } catch (error) {
    console.error('Error fetching student unread count:', error);
    throw error;
  }
};

// Mark a specific notification as read for students
export const markStudentNotificationAsRead = async (notificationId: string): Promise<void> => {
  try {
    const headers = await getCommonHeaders();
    const url = `${apiUrl}/notifications/students/mark-read/${notificationId}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
  } catch (error) {
    console.error('Error marking student notification as read:', error);
    throw error;
  }
};

// Mark all notifications as read for students
export const markAllStudentNotificationsAsRead = async (): Promise<void> => {
  try {
    const headers = await getCommonHeaders();
    const url = `${apiUrl}/notifications/students/mark-all-read`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
  } catch (error) {
    console.error('Error marking all student notifications as read:', error);
    throw error;
  }
};

// Delete all notifications for students
export const deleteAllStudentNotifications = async (): Promise<void> => {
  try {
    const headers = await getCommonHeaders();
    const url = `${apiUrl}/notifications/students/delete-all`;
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
  } catch (error) {
    console.error('Error deleting all student notifications:', error);
    throw error;
  }
};

// Helper function to get notification icon based on type
export const getNotificationIcon = (type: string): string => {
  switch (type) {
    case 'STUDENT_ACCOUNT_CREATED':
      return 'person-add';
    case 'STUDENT_PROFILE_APPROVED':
      return 'checkmark-circle';
    case 'STUDENT_PROFILE_REJECTED':
      return 'close-circle';
    case 'STUDENT_COIN_PURCHASE':
      return 'card';
    case 'STUDENT_UWHIZ_PARTICIPATION':
      return 'trophy';
    case 'STUDENT_CHAT_MESSAGE':
      return 'chatbubble';
    default:
      return 'notifications';
  }
};

// Helper function to get notification color based on type
export const getNotificationColor = (type: string): string => {
  switch (type) {
    case 'STUDENT_PROFILE_APPROVED':
      return '#4CAF50';
    case 'STUDENT_PROFILE_REJECTED':
      return '#F44336';
    case 'STUDENT_COIN_PURCHASE':
      return '#FF9800';
    case 'STUDENT_UWHIZ_PARTICIPATION':
      return '#FFD700';
    case 'STUDENT_CHAT_MESSAGE':
      return '#2196F3';
    default:
      return '#6B7280';
  }
};
