import AsyncStorage from '@react-native-async-storage/async-storage';
import axiosInstance from '../config/axios';

export interface Notification {
  id: string;
  userId: string;
  userType: 'STUDENT' | 'CLASS' | 'ADMIN';
  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |
        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |
        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |
        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |
        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |
        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |
        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationPagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface NotificationResponse {
  notifications: Notification[];
  pagination: NotificationPagination;
}

// Get student notifications
export const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {
  try {
    console.log('Fetching notifications with params:', { page, limit });

    const response = await axiosInstance.get('/notifications/students', {
      params: { page, limit }
    });

    console.log('Notification response:', response.data);

    // Handle different response structures
    if (response.data.success && response.data.data) {
      return response.data.data;
    } else if (response.data.notifications) {
      return response.data;
    } else {
      console.warn('Unexpected response structure:', response.data);
      return {
        notifications: [],
        pagination: {
          currentPage: page,
          totalPages: 1,
          totalCount: 0,
          limit,
          hasNextPage: false,
          hasPrevPage: false,
        },
      };
    }
  } catch (error) {
    console.error('Error fetching student notifications:', error);
    throw error;
  }
};

// Get unread notification count for students
export const getStudentUnreadCount = async (): Promise<number> => {
  try {
    console.log('Fetching unread count...');

    const response = await axiosInstance.get('/notifications/students/count');

    console.log('Unread count response:', response.data);

    // Handle different response structures
    if (response.data.success && response.data.data && typeof response.data.data.count === 'number') {
      return response.data.data.count;
    } else if (typeof response.data.count === 'number') {
      return response.data.count;
    } else {
      console.warn('Unexpected unread count response structure:', response.data);
      return 0;
    }
  } catch (error) {
    console.error('Error fetching student unread count:', error);
    return 0; // Return 0 instead of throwing to prevent UI crashes
  }
};

// Mark a specific notification as read for students
export const markStudentNotificationAsRead = async (notificationId: string): Promise<void> => {
  try {
    await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);
  } catch (error) {
    console.error('Error marking student notification as read:', error);
    throw error;
  }
};

// Mark all notifications as read for students
export const markAllStudentNotificationsAsRead = async (): Promise<void> => {
  try {
    await axiosInstance.post('/notifications/students/mark-all-read');
  } catch (error) {
    console.error('Error marking all student notifications as read:', error);
    throw error;
  }
};

// Delete all notifications for students
export const deleteAllStudentNotifications = async (): Promise<void> => {
  try {
    await axiosInstance.delete('/notifications/students/delete-all');
  } catch (error) {
    console.error('Error deleting all student notifications:', error);
    throw error;
  }
};

// Helper function to get notification icon based on type
export const getNotificationIcon = (type: string): string => {
  switch (type) {
    case 'STUDENT_ACCOUNT_CREATED':
      return 'person-add';
    case 'STUDENT_PROFILE_APPROVED':
      return 'checkmark-circle';
    case 'STUDENT_PROFILE_REJECTED':
      return 'close-circle';
    case 'STUDENT_COIN_PURCHASE':
      return 'card';
    case 'STUDENT_UWHIZ_PARTICIPATION':
      return 'trophy';
    case 'STUDENT_CHAT_MESSAGE':
      return 'chatbubble';
    default:
      return 'notifications';
  }
};

// Helper function to get notification color based on type
export const getNotificationColor = (type: string): string => {
  switch (type) {
    case 'STUDENT_PROFILE_APPROVED':
      return '#4CAF50';
    case 'STUDENT_PROFILE_REJECTED':
      return '#F44336';
    case 'STUDENT_COIN_PURCHASE':
      return '#FF9800';
    case 'STUDENT_UWHIZ_PARTICIPATION':
      return '#FFD700';
    case 'STUDENT_CHAT_MESSAGE':
      return '#2196F3';
    default:
      return '#6B7280';
  }
};

// Test function to verify API connectivity and authentication
export const testNotificationAPI = async (): Promise<void> => {
  try {
    console.log('=== Testing Notification API ===');

    // Check if token exists
    const token = await AsyncStorage.getItem('token');
    console.log('Token exists:', !!token);
    console.log('Token preview:', token ? `${token.substring(0, 20)}...` : 'No token');

    // Test API connectivity using Axios
    const response = await axiosInstance.get('/notifications/students/count');

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);
    console.log('Response data:', response.data);

    console.log('=== End Test ===');
  } catch (error: any) {
    console.error('Test failed:', error);
    if (error.response) {
      console.error('Error response status:', error.response.status);
      console.error('Error response data:', error.response.data);
    }
  }
};

// Helper function to refresh notification count
const refreshNotificationCount = () => {
  try {
    if ((global as any).refreshNotificationCount) {
      (global as any).refreshNotificationCount();
    }
  } catch (error) {
    console.log('Could not refresh notification count:', error);
  }
};

// Trigger notification for coin purchase
export const triggerCoinPurchaseNotification = async (amount: number, coins: number): Promise<void> => {
  try {
    await axiosInstance.post('/notifications/create-single', {
      userType: 'STUDENT',
      type: 'STUDENT_COIN_PURCHASE',
      title: 'Coins Purchased Successfully!',
      message: `You have successfully purchased ${coins} coins for ₹${amount}. Your coins have been added to your account.`,
      data: {
        actionType: 'OPEN_WALLET',
        amount,
        coins,
        timestamp: new Date().toISOString(),
      },
    });
    console.log('Coin purchase notification triggered successfully');
    refreshNotificationCount();
  } catch (error) {
    console.error('Error triggering coin purchase notification:', error);
  }
};

// Trigger notification for profile updates
export const triggerProfileUpdateNotification = async (updateType: string): Promise<void> => {
  try {
    await axiosInstance.post('/notifications/create-single', {
      userType: 'STUDENT',
      type: 'STUDENT_PROFILE_APPROVED',
      title: 'Profile Updated',
      message: `Your ${updateType} has been updated successfully.`,
      data: {
        actionType: 'OPEN_PROFILE',
        updateType,
        timestamp: new Date().toISOString(),
      },
    });
    console.log('Profile update notification triggered successfully');
    refreshNotificationCount();
  } catch (error) {
    console.error('Error triggering profile update notification:', error);
  }
};

// Trigger notification for exam participation
export const triggerExamParticipationNotification = async (examName: string, result?: string): Promise<void> => {
  try {
    const title = result ? 'Exam Results Available!' : 'Exam Participation Confirmed';
    const message = result
      ? `Your results for ${examName} are now available. ${result}`
      : `You have successfully registered for ${examName}. Good luck!`;

    await axiosInstance.post('/notifications/create-single', {
      userType: 'STUDENT',
      type: 'STUDENT_UWHIZ_PARTICIPATION',
      title,
      message,
      data: {
        actionType: 'OPEN_EXAM_RESULTS',
        examName,
        result,
        timestamp: new Date().toISOString(),
      },
    });
    console.log('Exam participation notification triggered successfully');
    refreshNotificationCount();
  } catch (error) {
    console.error('Error triggering exam participation notification:', error);
  }
};

// Trigger notification for wishlist actions
export const triggerWishlistNotification = async (action: 'added' | 'removed', className: string): Promise<void> => {
  try {
    const title = action === 'added' ? 'Added to Wishlist' : 'Removed from Wishlist';
    const message = action === 'added'
      ? `${className} has been added to your wishlist.`
      : `${className} has been removed from your wishlist.`;

    await axiosInstance.post('/notifications/create-single', {
      userType: 'STUDENT',
      type: 'STUDENT_ACCOUNT_CREATED', // Using generic type for wishlist actions
      title,
      message,
      data: {
        actionType: 'OPEN_WISHLIST',
        className,
        action,
        timestamp: new Date().toISOString(),
      },
    });
    console.log('Wishlist notification triggered successfully');
    refreshNotificationCount();
  } catch (error) {
    console.error('Error triggering wishlist notification:', error);
  }
};

// Trigger notification for review submission
export const triggerReviewSubmissionNotification = async (className: string, rating: number): Promise<void> => {
  try {
    await axiosInstance.post('/notifications/create-single', {
      userType: 'STUDENT',
      type: 'STUDENT_ACCOUNT_CREATED', // Using generic type for review actions
      title: 'Review Submitted',
      message: `Thank you for reviewing ${className}! Your ${rating}-star review has been submitted.`,
      data: {
        actionType: 'OPEN_REVIEWS',
        className,
        rating,
        timestamp: new Date().toISOString(),
      },
    });
    console.log('Review submission notification triggered successfully');
    refreshNotificationCount();
  } catch (error) {
    console.error('Error triggering review submission notification:', error);
  }
};
