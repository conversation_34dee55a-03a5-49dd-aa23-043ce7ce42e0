import AsyncStorage from '@react-native-async-storage/async-storage';
import {apiUrl} from '../config/apiUrl';

// Helper function to construct API URL
const getApiUrl = (endpoint: string): string => {
  const baseUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${cleanEndpoint}`;
};

export interface Notification {
  id: string;
  userId: string;
  userType: 'STUDENT' | 'CLASS' | 'ADMIN';
  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |
        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |
        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |
        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |
        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |
        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |
        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationPagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface NotificationResponse {
  notifications: Notification[];
  pagination: NotificationPagination;
}

const getCommonHeaders = async () => {
  const token = await AsyncStorage.getItem('token');
  return {
    'Content-Type': 'application/json',
    Accept: '*/*',
    'client-type': 'uest-mobile-app',
    Authorization: token ? `Bearer ${token}` : '',
  };
};

// Get student notifications
export const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {
  try {
    const headers = await getCommonHeaders();
    const url = getApiUrl(`/notifications/students?page=${page}&limit=${limit}`);

    console.log('Fetching notifications from:', url);
    console.log('Headers:', headers);

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const result = await response.json();
    console.log('Notification response:', result);

    // Handle different response structures
    if (result.success && result.data) {
      return result.data;
    } else if (result.notifications) {
      return result;
    } else {
      console.warn('Unexpected response structure:', result);
      return {
        notifications: [],
        pagination: {
          currentPage: page,
          totalPages: 1,
          totalCount: 0,
          limit,
          hasNextPage: false,
          hasPrevPage: false,
        },
      };
    }
  } catch (error) {
    console.error('Error fetching student notifications:', error);
    throw error;
  }
};

// Get unread notification count for students
export const getStudentUnreadCount = async (): Promise<number> => {
  try {
    const headers = await getCommonHeaders();
    const url = getApiUrl('/notifications/students/count');

    console.log('Fetching unread count from:', url);
    console.log('Headers:', headers);

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    console.log('Unread count response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const result = await response.json();
    console.log('Unread count response:', result);

    // Handle different response structures
    if (result.success && result.data && typeof result.data.count === 'number') {
      return result.data.count;
    } else if (typeof result.count === 'number') {
      return result.count;
    } else {
      console.warn('Unexpected unread count response structure:', result);
      return 0;
    }
  } catch (error) {
    console.error('Error fetching student unread count:', error);
    return 0; // Return 0 instead of throwing to prevent UI crashes
  }
};

// Mark a specific notification as read for students
export const markStudentNotificationAsRead = async (notificationId: string): Promise<void> => {
  try {
    const headers = await getCommonHeaders();
    const url = getApiUrl(`/notifications/students/mark-read/${notificationId}`);

    const response = await fetch(url, {
      method: 'POST',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
  } catch (error) {
    console.error('Error marking student notification as read:', error);
    throw error;
  }
};

// Mark all notifications as read for students
export const markAllStudentNotificationsAsRead = async (): Promise<void> => {
  try {
    const headers = await getCommonHeaders();
    const url = getApiUrl('/notifications/students/mark-all-read');

    const response = await fetch(url, {
      method: 'POST',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
  } catch (error) {
    console.error('Error marking all student notifications as read:', error);
    throw error;
  }
};

// Delete all notifications for students
export const deleteAllStudentNotifications = async (): Promise<void> => {
  try {
    const headers = await getCommonHeaders();
    const url = getApiUrl('/notifications/students/delete-all');

    const response = await fetch(url, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
  } catch (error) {
    console.error('Error deleting all student notifications:', error);
    throw error;
  }
};

// Helper function to get notification icon based on type
export const getNotificationIcon = (type: string): string => {
  switch (type) {
    case 'STUDENT_ACCOUNT_CREATED':
      return 'person-add';
    case 'STUDENT_PROFILE_APPROVED':
      return 'checkmark-circle';
    case 'STUDENT_PROFILE_REJECTED':
      return 'close-circle';
    case 'STUDENT_COIN_PURCHASE':
      return 'card';
    case 'STUDENT_UWHIZ_PARTICIPATION':
      return 'trophy';
    case 'STUDENT_CHAT_MESSAGE':
      return 'chatbubble';
    default:
      return 'notifications';
  }
};

// Helper function to get notification color based on type
export const getNotificationColor = (type: string): string => {
  switch (type) {
    case 'STUDENT_PROFILE_APPROVED':
      return '#4CAF50';
    case 'STUDENT_PROFILE_REJECTED':
      return '#F44336';
    case 'STUDENT_COIN_PURCHASE':
      return '#FF9800';
    case 'STUDENT_UWHIZ_PARTICIPATION':
      return '#FFD700';
    case 'STUDENT_CHAT_MESSAGE':
      return '#2196F3';
    default:
      return '#6B7280';
  }
};

// Test function to verify API connectivity and authentication
export const testNotificationAPI = async (): Promise<void> => {
  try {
    console.log('=== Testing Notification API ===');

    // Check if token exists
    const token = await AsyncStorage.getItem('token');
    console.log('Token exists:', !!token);
    console.log('Token preview:', token ? `${token.substring(0, 20)}...` : 'No token');

    // Test API connectivity
    const headers = await getCommonHeaders();
    console.log('Request headers:', headers);

    const url = getApiUrl('/notifications/students/count');
    console.log('Testing URL:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('Response body:', responseText);

    if (response.ok) {
      try {
        const jsonData = JSON.parse(responseText);
        console.log('Parsed JSON:', jsonData);
      } catch (parseError) {
        console.error('Failed to parse JSON:', parseError);
      }
    }

    console.log('=== End Test ===');
  } catch (error) {
    console.error('Test failed:', error);
  }
};
