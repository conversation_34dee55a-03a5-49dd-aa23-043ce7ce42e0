/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState, useRef} from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  Image,
  View,
  FlatList,
  Dimensions,
  NativeSyntheticEvent,
  NativeScrollEvent,
} from 'react-native';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {IMAGE_CONSTANT, PrimaryColors} from '../../../Utils/Constants';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import {useNavigation} from '@react-navigation/native';
import IndexStyle from '../../../Theme/IndexStyle';
import Icon from 'react-native-vector-icons/MaterialIcons';

import AsyncStorage from '@react-native-async-storage/async-storage';
import {imgBaseUrl} from '../../../config/apiUrl';
import {ScrollView} from 'react-native-gesture-handler';

import FontAwesome from 'react-native-vector-icons/FontAwesome';
import {getThoughts} from '../../../services/thoughtService';
import {getStudentProfileData} from '../../../services/studentProfileService';
import {getCategoryCounts} from '../../../services/categoryService';
import {getClassList} from '../../../services/classService';
import NotificationBell from '../../../CommonComponents/NotificationBell';

interface CategoryItem {
  id: string;
  name: string;
  image: any;
  totalClasses: number;
  backgroundcolor: string;
}
interface ClassItem {
  id: string;
  firstName?: string;
  lastName?: string;
  className?: string;
  tuitionClasses?: {education?: string; coachingType?: string}[];
  ClassAbout?: {profilePhoto?: string; classesLogo?: string};
  averageRating?: number;
  reviewCount?: number;
}
interface ThoughtItem {
  id: string;
  thoughts: string;
  createdAt: string;
  status: string;
  class: {
    id: string;
    className: string;
    firstName: string;
    lastName: string;
    contactNo: string;
    ClassAbout: {
      classesLogo: string;
    };
  };
}

const Home: React.FC = () => {
  const screenWidth = Dimensions.get('window').width;
  const flatListRef = useRef<FlatList>(null);
  const scrollTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollInterval = 2000;
  const navigation = useNavigation<any>();
  const {isDarkMode} = IndexStyle();
  const [classData, setClassData] = useState<ClassItem[]>([]);
  const [categorydata, setCategorydata] = useState<any>();
  const [categoryList, setCategoryList] = useState<CategoryItem[]>([]);
  const [thought, setThought] = useState<any>([]);
  const [thoughtIndex, setThoughtIndex] = useState(0);
  const [storedUserData, setStoredUserData] = useState<any>(null);
  const thoughtListRef = useRef<FlatList>(null);

  useEffect(() => {
    const updateCategoryList = () => {
      const newCategoryList: CategoryItem[] = [
        {
          id: '1',
          name: 'Education',
          image: IMAGE_CONSTANT.EDUCATION,
          totalClasses: categorydata?.Education || 0,
          backgroundcolor: '#BDC9D7',
        },
        {
          id: '2',
          name: 'Drama',
          image: IMAGE_CONSTANT.DRAMA,
          totalClasses: categorydata?.Drama || 0,
          backgroundcolor: '#EBE2E3',
        },
        {
          id: '3',
          name: 'Music',
          image: IMAGE_CONSTANT.MUSIC,
          totalClasses: categorydata?.Music || 0,
          backgroundcolor: '#E5E1E2',
        },
        {
          id: '4',
          name: 'Art & Craft',
          image: IMAGE_CONSTANT.ARTANDCRAFT,
          totalClasses: categorydata?.['Art & Craft'] || 0,
          backgroundcolor: '#FDF9EE',
        },
        {
          id: '5',
          name: 'Sports',
          image: IMAGE_CONSTANT.SPORTS,
          totalClasses: categorydata?.Sports || 0,
          backgroundcolor: '#D7CBCB',
        },
        {
          id: '6',
          name: 'Languages',
          image: IMAGE_CONSTANT.LANGUAGE,
          totalClasses: categorydata?.Languages || 0,
          backgroundcolor: '##D7CBCB',
        },
        {
          id: '7',
          name: 'Technology',
          image: IMAGE_CONSTANT.TECHNOLOGY,
          totalClasses: categorydata?.Technology || 0,
          backgroundcolor: '#FBF9EA',
        },
        {
          id: '9',
          name: 'Dance',
          image: IMAGE_CONSTANT.DANCE,
          totalClasses: categorydata?.Arts || 0,
          backgroundcolor: '#FDFAEB',
        },
        {
          id: '10',
          name: 'Computer Classes',
          image: IMAGE_CONSTANT.COMPUTER,
          totalClasses: categorydata?.['Computer Classes'] || 0,
          backgroundcolor: '#FBF5DF',
        },
        {
          id: '11',
          name: 'Cooking Class',
          image: IMAGE_CONSTANT.COOKING,
          totalClasses: categorydata?.['Cooking Class'] || 0,
          backgroundcolor: '#FDF6E6',
        },
        {
          id: '12',
          name: 'Garba Classes',
          image: IMAGE_CONSTANT.GARBA,
          totalClasses: categorydata?.['Garba Classes'] || 0,
          backgroundcolor: '#E4DFDB',
        },
        {
          id: '13',
          name: 'Vaidik Maths',
          image: IMAGE_CONSTANT.VAIDIKMATHS,
          totalClasses: categorydata?.['Vaidik Maths'] || 0,
          backgroundcolor: '#C6DBBA',
        },
        {
          id: '14',
          name: 'Gymnastic Classes',
          image: IMAGE_CONSTANT.GYMNASTIC,
          totalClasses: categorydata?.['Gymnastic Classes'] || 0,
          backgroundcolor: '#FBF9EA',
        },
        {
          id: '15',
          name: 'Yoga Classes',
          image: IMAGE_CONSTANT.YOGA,
          totalClasses: categorydata?.['Yoga Classes'] || 0,
          backgroundcolor: '#E4DFDB',
        },
        {
          id: '16',
          name: 'Aviation Classes',
          image: IMAGE_CONSTANT.AVIATION,
          totalClasses: categorydata?.['Aviation Classes'] || 0,
          backgroundcolor: '#C6DBBA',
        },
        {
          id: '17',
          name: 'Designing Classes',
          image: IMAGE_CONSTANT.DESIGNING,
          totalClasses: categorydata?.['Designing Classes'] || 0,
          backgroundcolor: '#C6DBBA',
        },
      ];
      setCategoryList(newCategoryList);
    };

    if (categorydata) {
      updateCategoryList();
    }
  }, [categorydata]);

  const startAutoScroll = () => {
    if (scrollTimerRef.current) {
      clearInterval(scrollTimerRef.current);
    }
    scrollTimerRef.current = setInterval(() => {
      let nextIndex = currentIndex + 1;
      if (nextIndex >= categoryList.length) {
        nextIndex = 0;
      }

      flatListRef.current?.scrollToIndex({
        index: nextIndex,
        animated: true,
      });
      setCurrentIndex(nextIndex);
    }, scrollInterval);
  };

  useEffect(() => {
    if (categoryList.length === 0) {
      return;
    }
    startAutoScroll();
    return () => {
      if (scrollTimerRef.current) {
        clearInterval(scrollTimerRef.current);
      }
    };
  });
  useEffect(() => {
    const interval = setInterval(() => {
      if (!thought.thoughts || thought.thoughts.length === 0) {
        return;
      }

      const nextIndex = (thoughtIndex + 1) % thought.thoughts.length;

      thoughtListRef.current?.scrollToIndex({
        index: nextIndex,
        animated: true,
      });

      setThoughtIndex(nextIndex);
    }, 3000);

    return () => clearInterval(interval);
  }, [thoughtIndex, thought.thoughts]);

  const handleThoughtScroll = (
    event: NativeSyntheticEvent<NativeScrollEvent>,
  ) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(offsetX / (screenWidth * 0.8));
    setThoughtIndex(newIndex);
  };
  const handleScrollBegin = () => {
    if (scrollTimerRef.current) {
      clearInterval(scrollTimerRef.current);
    }
  };

  const fetchStoredUserData = async () => {
    try {
      const userDataString = await AsyncStorage.getItem('userData');
      console.log('Raw AsyncStorage userData:', userDataString);
      if (userDataString) {
        const userData = JSON.parse(userDataString);
        console.log('Parsed userData:', userData);
        setStoredUserData(userData.data);
      } else {
        console.log('No userData found in AsyncStorage');
      }
    } catch (error) {
      console.error('Error retrieving userData from AsyncStorage:', error);
    }
  };

  useEffect(() => {
    fetchStoredUserData();
    fetchStudentProfileData();
    fetchClassList();
    getCategorycount();
    fetchThoughts();
  }, []);

  const fetchThoughts = async () => {
    try {
      const data = await getThoughts();
      setThought(data);
    } catch (error) {
      console.error('Error fetching thoughts:', error);
    }
  };

  const ITEM_WIDTH = screenWidth * 0.8;

  const renderThoughtItem = ({item}: {item: ThoughtItem}) => {
    const classLogo = item.class.ClassAbout.classesLogo;
    const imageUrl = classLogo ? `${imgBaseUrl}/${classLogo}` : null;
    const teacherName = `${item.class.firstName} ${item.class.lastName}`;
    const className = item.class.className;
    return (
      <View
        style={{
          width: ITEM_WIDTH,
          marginHorizontal: 8,
          backgroundColor: isDarkMode ? '#1C1C1E' : '#FFFFFF',
          borderRadius: 16,
          padding: 15,
          shadowOffset: {width: 0, height: 4},
          shadowOpacity: 0.08,
          shadowRadius: 12,
          elevation: 6,
          borderWidth: 0.5,
          borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
        }}>
        <View style={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: 60,
          height: 60,
          justifyContent: 'center',
          alignItems: 'center',
          opacity: 0.1,
        }}>
          <FontAwesome
            name="quote-right"
            size={40}
            color={'#FD904B'}
          />
        </View>

        <Text
          style={{
            fontSize: 14,
            lineHeight: 22,
            color: isDarkMode ? '#D9D9D9' : '#555',
            textAlign: 'left',
            marginBottom: 20,
            fontStyle: 'italic',
          }}>
          "{item.thoughts}"
        </Text>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: 10,
            borderTopWidth: 0.5,
            borderTopColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
            paddingTop: 16,
          }}>
          {imageUrl ? (
            <Image
              source={{uri: imageUrl}}
              style={{width: 40, height: 40, borderRadius: 20}}
              resizeMode="cover"
            />
          ) : (
            <View
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: 'rgba(253, 144, 75, 0.1)',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text style={{fontSize: 16, color: '#FD904B', fontWeight: '600'}}>{teacherName.charAt(0)}</Text>
            </View>
          )}
          <View style={{marginLeft: 12}}>
            <Text
              style={{
                fontSize: 14,
                fontWeight: '600',
                color: isDarkMode ? '#FFFFFF' : '#1F1F39',
              }}>
              {teacherName}
            </Text>
            <Text
              style={{
                fontSize: 12,
                color: isDarkMode ? '#B0B0B0' : '#666',
                marginTop: 2,
              }}>
              {className}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const fetchStudentProfileData = async () => {
    try {
      const data = await getStudentProfileData();
      const profile = data.data.profile || {};
      console.log('PROFILE DATA OBJECT HOME SCREEN:::', profile);
      console.log('PARSED DATA:', data);
    } catch (err) {
      console.log('ERROR IN GET STUDENT PROFILES DATA:', err);
    }
  };

  const OnProfile = (classId: string) => {
    console.log('ON CLICK PROFILE');
    navigation.navigate('TutorProfile', {classId: classId});
  };

  const fetchClassList = async (pageNumber: number = 1) => {
    try {
      const data = await getClassList(pageNumber, 5);
      setClassData(data.data);
    } catch (err) {
      console.log('ERROR IN GET CLASS LIST::', err);
    }
  };

  const getCategorycount = async () => {
    try {
      const data = await getCategoryCounts();
      setCategorydata(data);
    } catch (error) {
      console.error('Error fetching category counts:', error);
    }
  };

  const renderItem = ({item}: {item: ClassItem}) => {
    const profilePhoto = item.ClassAbout?.classesLogo;
    const imageUrl = profilePhoto ? `${imgBaseUrl}/${profilePhoto}` : null;
    const rating = item.averageRating ?? 'N/A';
    const reviewCount = item.reviewCount ?? 'N/A';

    return (
      <TouchableOpacity
        style={[
          BaseStyle.premiumTutorCard,
          {
            backgroundColor: isDarkMode ? '#1C1C1E' : PrimaryColors.WHITE,
            shadowColor: isDarkMode ? '#000000' : '#000000',
          },
        ]}
        onPress={() => {
          OnProfile(item.id);
        }}>
        <View style={BaseStyle.premiumTutorImageSection}>
          <View style={BaseStyle.premiumTutorImageContainer}>
            {imageUrl ? (
              <Image
                source={{uri: imageUrl}}
                style={BaseStyle.premiumTutorImage}
                resizeMode="cover"
              />
            ) : (
              <View style={BaseStyle.premiumTutorPlaceholder}>
                <Icon name="person" size={40} color="#FD904B" />
              </View>
            )}
          </View>
          <View style={BaseStyle.premiumTutorVerifiedBadge}>
            <Ionicons name="checkmark-circle" size={20} color="#FD904B" />
          </View>
        </View>
        <View style={BaseStyle.premiumTutorContent}>
          <Text style={[BaseStyle.premiumTutorName, {
            color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
          }]}>
            {(item.firstName ?? '') + ' ' + (item.lastName ?? '')}
          </Text>
          <View style={BaseStyle.premiumTutorRatingRow}>
            <View style={BaseStyle.premiumTutorRating}>
              <Image
                resizeMode="contain"
                source={IMAGE_CONSTANT.STAR}
                style={BaseStyle.premiumTutorStarIcon}
              />
              <Text style={BaseStyle.premiumTutorRatingText}>
                {`${rating}.0`}
              </Text>
            </View>
            <Text style={BaseStyle.premiumTutorReviewCount}>
              {`${reviewCount} reviews`}
            </Text>
          </View>
          <View style={BaseStyle.premiumTutorSpecialty}>
            <Text style={BaseStyle.premiumTutorSpecialtyText}>
              {item.className || 'Expert Educator'}
            </Text>
          </View>
        </View>
        <View style={BaseStyle.premiumTutorActionSection}>
          {/* <TouchableOpacity
            style={BaseStyle.premiumTutorActionButton}
            onPress={() => OnProfile(item.id)}>
            <Feather name="arrow-right" size={20} color={'#FFFFFF'} />
          </TouchableOpacity> */}
        </View>
      </TouchableOpacity>
    );
  };

  const renderCategoryItem = ({item}: {item: CategoryItem}) => (
    <TouchableOpacity
      style={[
        BaseStyle.premiumCategoryCard,
        {
          backgroundColor: isDarkMode ? '#1C1C1E' : PrimaryColors.WHITE,
          shadowColor: isDarkMode ? '#000000' : '#000000',
        },
      ]}
      onPress={() => {
        console.log('ON CLICK CATEGFORY::::', item.name);
        // navigation.navigate('ClassList',{category:item.name});
        navigation.navigate(strings.Home.SEARCH, {category: item.name});
      }}>
      <View style={BaseStyle.premiumCategoryImageSection}>
        <View style={[BaseStyle.premiumCategoryImageBackground, {
          backgroundColor: isDarkMode ? 'rgba(253, 144, 75, 0.15)' : 'rgba(253, 144, 75, 0.1)',
        }]}>
          <Image
            source={item.image}
            style={BaseStyle.premiumCategoryImage}
            resizeMode="contain"
          />
        </View>
        <View style={BaseStyle.premiumCategoryBadge}>
          <Text style={BaseStyle.premiumCategoryBadgeText}>Popular</Text>
        </View>
      </View>
      <View style={BaseStyle.premiumCategoryTextSection}>
        <Text style={[BaseStyle.premiumCategoryTitle, {
          color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
        }]} numberOfLines={2}>
          {item.name}
        </Text>
        <Text style={BaseStyle.premiumCategorySubtitle}>
          {item.totalClasses} courses • Expert instructors
        </Text>
      </View>
    </TouchableOpacity>
  );

  const quickLinks = [
    {title: 'Uwhiz', icon: IMAGE_CONSTANT.UWHIZLOGO},
    {title: 'Daily Quiz', icon: IMAGE_CONSTANT.DAILYQUIZ},
    {title: 'Find Classes', icon: IMAGE_CONSTANT.SEARCHCLASSES},
    {title: 'Profile', icon: IMAGE_CONSTANT.PROFILE},
  ];

  const firstName = storedUserData?.user?.firstName ?? 'Guest';
  const lastName = storedUserData?.user?.lastName ?? '';
  return (
    <SafeAreaProvider>
      <View style={BaseStyle.modernHeader}>
        <View style={BaseStyle.headerContent}>
          <View style={BaseStyle.userSection}>
            <View style={BaseStyle.welcomeTextContainer}>
              <Text style={BaseStyle.welcomeText}>Welcome back,</Text>
              <Text
                numberOfLines={1}
                style={BaseStyle.modernUsername}>{`${firstName} ${lastName}`}</Text>
              <Text style={BaseStyle.modernSubtitle}>Let's start learning today</Text>
            </View>
          </View>
          <View style={BaseStyle.headerActions}>
            <NotificationBell
              iconColor="#FFFFFF"
              iconSize={26}
              style={BaseStyle.modernActionButton}
            />
            <TouchableOpacity
              style={BaseStyle.modernActionButton}
              onPress={() => {
                navigation.navigate('Setting');
              }}>
              <Ionicons
                name="person-circle-outline"
                size={28}
                color={'#FFFFFF'}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <ScrollView
        nestedScrollEnabled={true}
        style={{
          backgroundColor: isDarkMode
            ? PrimaryColors.BLACK
            : PrimaryColors.WHITE,
        }}>
        <SafeAreaView
          style={{flex: 1, width: '100%', backgroundColor: PrimaryColors.BLACK}}
          edges={['left', 'right']}>
          <View
            style={{
              backgroundColor: isDarkMode
                ? PrimaryColors.BLACK
                : PrimaryColors.WHITE,
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
            }}>
            <View style={BaseStyle.premiumQuickLinksSection}>
              <View style={BaseStyle.premiumSectionHeader}>
                <Text style={[BaseStyle.premiumSectionTitle, {
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }]}>
                  Quick Links
                </Text>
                <Text style={BaseStyle.premiumSectionSubtitle}>
                  Access your favorite features instantly
                </Text>
              </View>
              <View style={BaseStyle.premiumQuickLinksGrid}>
                {quickLinks.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      BaseStyle.premiumQuickLinkCard,
                      {
                        backgroundColor: isDarkMode ? '#1C1C1E' : PrimaryColors.WHITE,
                        shadowColor: isDarkMode ? '#000000' : '#000000',
                      },
                    ]}
                    onPress={() => console.log(item.title)}>
                    <View style={BaseStyle.premiumQuickLinkIconWrapper}>
                      <View style={[BaseStyle.premiumQuickLinkIconContainer, {
                        backgroundColor: isDarkMode ? 'rgba(253, 144, 75, 0.15)' : 'rgba(253, 144, 75, 0.1)',
                      }]}>
                        <Image
                          resizeMode="contain"
                          source={item.icon}
                          style={BaseStyle.premiumQuickLinkIcon}
                        />
                      </View>
                    </View>
                    <View style={BaseStyle.premiumQuickLinkTextWrapper}>
                      <Text style={[BaseStyle.premiumQuickLinkText, {
                        color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                      }]}>
                        {item.title}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={BaseStyle.premiumCategorySection}>
              <View style={BaseStyle.premiumSectionHeader}>
                <Text style={[BaseStyle.premiumSectionTitle, {
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }]}>
                  Choose Your Category
                </Text>
                <Text style={BaseStyle.premiumSectionSubtitle}>
                  Explore our comprehensive learning paths
                </Text>
              </View>
              <FlatList
                ref={flatListRef}
                data={categoryList}
                horizontal
                showsHorizontalScrollIndicator={false}
                keyExtractor={item => item.id}
                contentContainerStyle={{paddingHorizontal: 16, paddingRight: 40}}
                onScrollBeginDrag={handleScrollBegin}
                decelerationRate="fast"
                snapToInterval={196}
                renderItem={renderCategoryItem}
              />
            </View>
            <View style={BaseStyle.premiumTutorSection}>
              <View style={BaseStyle.premiumSectionHeader}>
                <Text style={[BaseStyle.premiumSectionTitle, {
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }]}>
                  Meet Your Top Tutors
                </Text>
                <Text style={BaseStyle.premiumSectionSubtitle}>
                  Connect with expert educators in your field
                </Text>
              </View>
              <FlatList
                data={classData}
                keyExtractor={item => item.id}
                renderItem={renderItem}
                contentContainerStyle={{paddingBottom: 16}}
                showsVerticalScrollIndicator={false}
              />
            </View>
            <View style={BaseStyle.premiumCommunitySection}>
              <View style={BaseStyle.premiumSectionHeader}>
                <Text style={[BaseStyle.premiumSectionTitle, {
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }]}>
                  What Our Community Thinks
                </Text>
                <Text style={BaseStyle.premiumSectionSubtitle}>
                  Real experiences from our learning community
                </Text>
              </View>
              <FlatList
                ref={thoughtListRef}
                data={thought.thoughts || []}
                renderItem={renderThoughtItem}
                keyExtractor={(_, index) => index.toString()}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                snapToAlignment="center"
                decelerationRate="fast"
                snapToInterval={screenWidth * 0.8}
                contentContainerStyle={{
                  paddingHorizontal: screenWidth * 0.04,
                }}
                onScroll={handleThoughtScroll}
                scrollEventThrottle={16}
              />
            </View>
          </View>
        </SafeAreaView>
      </ScrollView>
    </SafeAreaProvider>
  );
};

export default Home;

const BaseStyle = StyleSheet.create({
  content: {
    flex: 1,
    paddingHorizontal: 10,
  },
  homeGrid: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
  gridWrapper: {
    width: '30%',
    height: '80%',
    marginBottom: 20,
    borderRadius: 16,
    backgroundColor: PrimaryColors.WHITE,
  },
  // Modern Header Styles
  modernHeader: {
    backgroundColor: PrimaryColors.BLACK,
    paddingTop: '12%',
    paddingBottom: 32,
    paddingHorizontal: 24,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 80,
  },
  userSection: {
    flex: 1,
    paddingRight: 16,
    justifyContent: 'center',
  },
  welcomeTextContainer: {
    flex: 1,
  },
  welcomeText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 16,
    fontWeight: '400',
    marginBottom: 4,
    letterSpacing: 0.2,
  },
  modernUsername: {
    color: PrimaryColors.WHITE,
    fontSize: 32,
    fontWeight: '700',
    marginBottom: 6,
    letterSpacing: -0.5,
    lineHeight: 36,
  },
  modernSubtitle: {
    color: '#FD904B',
    fontSize: 16,
    fontWeight: '500',
    letterSpacing: 0.1,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  modernActionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.12)',
  },
  coinButton: {
    backgroundColor: 'rgba(253, 144, 75, 0.12)',
    borderColor: 'rgba(253, 144, 75, 0.25)',
  },
  modernCoinIcon: {
    height: 26,
    width: 26,
  },
  // Legacy Header Styles (keeping for compatibility)
  headercomponent: {
    height: '18%',
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: 10,
    backgroundColor: PrimaryColors.BLACK,
    borderColor: PrimaryColors.WHITE,
    justifyContent: 'space-evenly',
  },
  headername: {
    borderColor: 'white',
    flexDirection: 'row',
    width: '70%',
    alignItems: 'center',
  },
  coinicon: {
    width: '10%',
    marginTop: '16%',
  },
  profileicon: {
    width: '10%',
    justifyContent: 'center',
    paddingRight: '2%',
    alignItems: 'flex-end',
  },
  username: {
    color: PrimaryColors.WHITE,
    fontSize: 30,
    fontWeight: 'bold',
  },
  nameEmailWrapper: {
    flexDirection: 'column',
  },
  email: {
    color: PrimaryColors.WHITE,
    fontSize: 14,
    marginTop: 2,
  },
  smallcoin: {
    height: 40,
    width: 40,
    tintColor: PrimaryColors.WHITE,
  },
  initialsCircle: {
    backgroundColor: PrimaryColors.BLACK,
    width: 30,
    height: 30,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 15,
  },
  cointext: {
    width: '10%',
    paddingBottom: '4%',
    justifyContent: 'flex-end',
    paddingRight: '2%',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    marginLeft: 10,
  },
  categoryCard: {
    marginLeft: 10,
    borderRadius: 25,

    marginTop: '2%',
  },
  categoryImage: {
    height: 120,
    width: 200,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 15,
    paddingHorizontal: 8,
    marginLeft: 16,
    marginTop: 12,
    width: '80%',
    backgroundColor: '#f2f2f2',
  },
  itemContainer: {
    flexDirection: 'row',
    borderRadius: 20,
    padding: 12,
    marginBottom: 12,
    alignItems: 'center',
  },
  imageWrapper: {
    width: '25%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    height: 80,
    width: 80,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    backgroundColor: '#e6e6e6',
  },
  image: {
    height: '100%',
    width: '100%',
  },
  infoWrapper: {
    width: '55%',
    paddingLeft: 8,
    justifyContent: 'center',
  },
  nameText: {
    fontSize: 14,
    fontWeight: '700',
    marginBottom: 2,
  },
  subText: {
    fontSize: 15,
    fontWeight: '500',
  },
  buttonWrapper: {
    width: '20%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    marginTop: '7%',
    paddingVertical: 2,
    alignItems: 'center',
    borderRadius: 6,
    backgroundColor: '#FFEBF0',
  },
  buttonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FF914D',
  },
  thoughtContainer: {
    width: '100%',
    flexDirection: 'row',
    backgroundColor: PrimaryColors.WHITE,
    borderRadius: 20,
    padding: 12,
    marginBottom: 12,
    alignItems: 'center',
    marginHorizontal: 10,
  },
  imageWrapperthought: {
    width: '25%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainerthought: {
    height: 60,
    width: 60,
    borderRadius: 30,
    borderColor: PrimaryColors.CARDBORDER,
    borderWidth: 0.5,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    backgroundColor: '#e6e6e6',
  },
  imagethought: {
    height: '100%',
    width: '100%',
  },
  contentWrapper: {
    width: '75%',
    paddingLeft: 10,
    justifyContent: 'center',
  },
  thoughtText: {
    fontSize: 16,
    fontWeight: '500',
    color: PrimaryColors.BLACK,
    marginBottom: 4,
  },
  className: {
    fontSize: 14,
    fontWeight: '600',
    color: PrimaryColors.BLACK,
  },
  teacherName: {
    fontSize: 12,
    fontWeight: '400',
    color: PrimaryColors.GRAYSHADOW,
    marginTop: 2,
  },
  shadowBox: {
    backgroundColor: PrimaryColors.WHITE,
    padding: 16,
    borderRadius: 20,
    borderWidth: 1,
  },
  blackshadowBox: {
    backgroundColor: PrimaryColors.BLACK,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#FFF',
    shadowOffset: {width: 3, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 2, // for Android shadow
  },
  grayshadowBox: {
    backgroundColor: '#1D1D1D',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#FFF',
    shadowOffset: {width: 3, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 2, // for Android shadow
  },
  textShadow: {
    marginBottom: '2%',
    color: '#1F1F39',
    textShadowColor: 'rgba(0, 0, 0, 0.30)',
    textShadowOffset: {width: 1, height: 2},
    textShadowRadius: 5,
  },
  examcard: {
    width: '90%',
    height: '100%',
    padding: '1%',
    borderRadius: 14,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sponserdview: {
    width: '100%',
    height: '15%',
    paddingLeft: '3%',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  examname: {
    width: '100%',
    height: '20%',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingLeft: '3%',
    paddingTop: '1%',
  },
  twoButton: {
    width: '100%',
    height: '20%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: '2%',
  },
  viewandapply: {
    flexDirection: 'row',

    padding: '2%',
    borderRadius: 10,
    width: '80%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerspon: {
    width: '100%',
    height: '15%',
    justifyContent: 'center',
    paddingLeft: '3%',
  },
  card: {
    width: 160,
    height: 100,
    borderRadius: 16,
    margin: 10,
    justifyContent: 'flex-end',
    overflow: 'visible',
    alignItems: 'center',
    paddingBottom: 12,
  },
  imagecard: {
    width: '150%',
    height: '150%',
    borderRadius: 16,
  },
  labelContainer: {
    position: 'absolute',
    bottom: 10,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 10,
  },
  labelText: {
    color: '#555',
    fontWeight: '600',
    fontSize: 14,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBox: {
    width: '80%',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 5,
    backgroundColor: PrimaryColors.ORANGEFORTOGGLE,
    paddingVertical: 10,
    borderRadius: 5,
    alignItems: 'center',
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  icon: {
    height: 40,
    width: 40,
    marginRight: 10,
  },
  quickCard: {
    width: '47%',
    marginBottom: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 15,
  },
  // Modern Quick Links Styles
  modernQuickLinksSection: {
    marginTop: 32,
    paddingHorizontal: 24,
  },
  modernSectionTitle: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 24,
    letterSpacing: -0.8,
  },
  modernQuickLinksGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  modernQuickLinkCard: {
    width: '47%',
    aspectRatio: 1.1,
    borderRadius: 24,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  modernQuickLinkIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  modernQuickLinkIcon: {
    height: 36,
    width: 36,
  },
  modernQuickLinkText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    letterSpacing: -0.3,
    lineHeight: 20,
  },
  // Premium Design System - Common Components
  premiumSectionHeader: {
    marginBottom: 20,
    paddingHorizontal: 24,
  },
  premiumSectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 6,
    letterSpacing: -0.4,
  },
  premiumSectionSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#888888',
    letterSpacing: -0.1,
  },

  // Premium Quick Links Section
  premiumQuickLinksSection: {
    marginTop: 24,
    marginBottom: 32,
    backgroundColor: 'transparent',
  },
  premiumQuickLinksGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    gap: 16,
  },
  premiumQuickLinkCard: {
    width: '47%',
    height: 100,
    borderRadius: 20,
    padding: 16,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  premiumQuickLinkIconWrapper: {
    marginBottom: 8,
  },
  premiumQuickLinkIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  premiumQuickLinkIcon: {
    width: 32,
    height: 32,
  },
  premiumQuickLinkTextWrapper: {
    alignItems: 'center',
  },
  premiumQuickLinkText: {
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: -0.2,
    textAlign: 'center',
  },

  // Premium Category Section
  premiumCategorySection: {
    marginTop: 24,
    marginBottom: 36,
    backgroundColor: 'transparent',
  },
  premiumCategoryCard: {
    width: 180,
    height: 220,
    borderRadius: 20,
    marginRight: 16,
    marginLeft: 8,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  premiumCategoryImageSection: {
    height: 140,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    paddingTop: 20,
  },
  premiumCategoryImageBackground: {
    width: 90,
    height: 90,
    borderRadius: 45,
    justifyContent: 'center',
    alignItems: 'center',
  },
  premiumCategoryImage: {
    width: 170,
    height: 100,
  },
  premiumCategoryBadge: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: '#FD904B',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 10,
  },
  premiumCategoryBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  premiumCategoryTextSection: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: 'center',
  },
  premiumCategoryTitle: {
    fontSize: 16,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 6,
    letterSpacing: -0.2,
    lineHeight: 20,
  },
  premiumCategorySubtitle: {
    fontSize: 12,
    fontWeight: '500',
    color: '#888888',
    textAlign: 'center',
  },

  // Premium Tutor Section
  premiumTutorSection: {
    // marginTop: 24,
    marginBottom: 40,
    // backgroundColor: 'transparent',
  },
  premiumTutorCard: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 20,
    padding: 20,
    marginBottom: 16,
    marginHorizontal: 24,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  premiumTutorImageSection: {
    position: 'relative',
    marginRight: 16,
  },
  premiumTutorImageContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    overflow: 'hidden',
    backgroundColor: '#F5F5F5',
  },
  premiumTutorImage: {
    width: '100%',
    height: '100%',
  },
  premiumTutorPlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(253, 144, 75, 0.1)',
  },
  premiumTutorVerifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  premiumTutorContent: {
    flex: 1,
  },
  premiumTutorName: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 6,
    letterSpacing: -0.2,
  },
  premiumTutorRatingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  premiumTutorRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  premiumTutorStarIcon: {
    height: 16,
    width: 16,
    marginRight: 4,
  },
  premiumTutorRatingText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FD904B',
  },
  premiumTutorReviewCount: {
    fontSize: 12,
    fontWeight: '400',
    color: '#888888',
  },
  premiumTutorSpecialty: {
    backgroundColor: 'rgba(253, 144, 75, 0.1)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  premiumTutorSpecialtyText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FD904B',
  },
  premiumTutorActionSection: {
    marginLeft: 16,
  },
  premiumTutorActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FD904B',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Premium Community Section
  premiumCommunitySection: {
    marginTop: 24,
    marginBottom: 40,
    backgroundColor: 'transparent',
  },

});
