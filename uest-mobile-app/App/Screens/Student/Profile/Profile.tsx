/* eslint-disable react-native/no-inline-styles */
import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  Dimensions,
  TextInput,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import api from '../../../config/api';
import IndexStyle from '../../../Theme/IndexStyle';
import DefaultClassesLogo from '../../../CommonComponents/DefaultClassesLogo';
import {imgBaseUrl} from '../../../config/apiUrl';
import CurvHeader from '../../../CommonComponents/CurvHeader';
import {IMAGE_CONSTANT, PrimaryColors} from '../../../Utils/Constants';
import Toast from 'react-native-simple-toast';
import Feather from 'react-native-vector-icons/Feather';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getProfileData } from '../../../services/classService';
import { getReviewData } from '../../../services/reviewService';
import {triggerWishlistNotification, triggerReviewSubmissionNotification} from '../../../services/notificationService';

const {height: SCREEN_HEIGHT} = Dimensions.get('window');

const Profile = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const {classId} = route.params;
  const [profileData, setProfileData] = useState<any>(null);
  const [imageError, setImageError] = useState(false);
  const {isDarkMode} = IndexStyle();
  const [showFullBio, setShowFullBio] = useState(false);
  const [avgRating, setAvgRating] = useState(0);
  const [totalReview, setTotalReview] = useState(0);
  const [activeTab, setActiveTab] = useState('Education');
  const [reviews, setReviews] = useState<any[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [rating, setRating] = useState(0);
  const [message, setMessage] = useState('');
  const [error, setError] = useState({rating: false, message: false});
  const [storedUserData, setStoredUserData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    getProfileDataHandler();
    ClassesAvgRating(classId);
    fetchReviewData();
    fetchStoredUserData();
  }, []);

  const getTrimmedBio = (text: string) => {
    const words = text.trim().split(/\s+/);
    if (words.length <= 50) {
      return text;
    }
    return words.slice(0, 50).join(' ') + '...';
  };

  const getProfileDataHandler = async () => {
    try {
      const data = await getProfileData(classId);
      setProfileData(data);
      console.log('class data', data);
    } catch (err) {
      console.log('Profile fetch error:', err);
    }
  };

  const formatArrayToString = (value: any) => {
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        if (Array.isArray(parsed)) {
          return parsed.join(', ');
        }
      } catch {
        return value;
      }
    }
    return '';
  };

  const SaveToList = () => {
    console.log('ON CLICK SAVE TO LIST');
    var data = {classId: classId};
    api.AddWishList.addWishlist(data)
      .then(async res => {
        const resData = await res.json();
        console.log('IN WISHLIST RESPONSE:::', resData, res.status);
        if (res.status === 200) {
          // Trigger notification for successful wishlist addition
          const className = profileData?.className || 'Class';
          await triggerWishlistNotification('added', className);

          navigation.navigate('Wishlist');
        } else if (res.status === 400 || res.status === 401) {
          Toast.show(resData.message, Toast.SHORT);
        } else if (res.status === 404) {
          Toast.show(resData.message, Toast.SHORT);
        } else {
          Toast.show(strings.ForgotPassword.SOMETHINGWENTWRONG, Toast.SHORT);
        }
      })
      .catch(err => {
        console.log('IN WISHLIST ERROR:::', err);
      });
  };

  const ClassesAvgRating = (data: any) => {
    api.GetClassesAvgRating.getStudentAvgRating(data)
      .then(async res => {
        const resData = await res.json();
        console.log('IN AVG RATING RESPONSE:::', resData, res.status);
        if (res.status === 200) {
          setAvgRating(resData.averageRating);
        } else if (res.status === 400 || res.status === 401) {
          Toast.show(resData.message, Toast.SHORT);
        } else if (res.status === 404) {
          Toast.show(resData.message, Toast.SHORT);
        } else {
          Toast.show(strings.ForgotPassword.SOMETHINGWENTWRONG, Toast.SHORT);
        }
      })
      .catch(err => {
        console.log('IN WISHLIST ERROR:::', err);
      });
  };

  const fetchReviewData = async (pageNumber = 1) => {
    try {
      const data = await getReviewData(classId, pageNumber, 10);
      setTotalReview(data.total);
      setReviews(data.reviews || []);
    } catch (err) {
      console.log('ERROR IN GET REVIEW DATA::', err);
    }
  };

  const fetchStoredUserData = async () => {
    try {
      const userDataString = await AsyncStorage.getItem('userData');
      console.log('Raw AsyncStorage userData:', userDataString);
      if (userDataString) {
        const userData = JSON.parse(userDataString);
        console.log('Parsed userData:', userData);
        setStoredUserData(userData.data);
      } else {
        console.log('No userData found in AsyncStorage');
      }
    }
    catch (error) {
      console.error('Error retrieving userData from AsyncStorage:', error);
    }
  };

  const handleRatingPress = (star: number) => {
    setRating(star);
    if (star > 0) {
      setError(prev => ({...prev, rating: false}));
    }
  };

  const handleSubmit = async () => {
    const messageError = !message.trim();
    const ratingError = rating === 0;

    setError({
      message: messageError,
      rating: ratingError,
    });

    if (messageError || ratingError) {
      return;
    }

    const studentId = storedUserData?.user?.id;
    const studentName = `${storedUserData?.user?.firstName} ${storedUserData?.user?.lastName}`;

    const alreadyReviewed = reviews.some(item => item.studentId === studentId);

    if (alreadyReviewed) {
      Toast.show('You have already submitted a review', Toast.LONG);
      return;
    }

    const addData = {
      rating,
      message,
      classId,
      studentId,
      studentName,
    };

    console.log('Adding REVIEW with:', addData);

    try {
      setIsLoading(true);

      const response = await api.SubmitReviewData.submitReviewData(addData);

      if (response.ok) {
        const resText = await response.text();
        const resJson = JSON.parse(resText);
        console.log('Review submit response:', resJson);

        Toast.show('Review submitted successfully!', Toast.SHORT);

        // Trigger notification for successful review submission
        const className = profileData?.className || 'Class';
        await triggerReviewSubmissionNotification(className, rating);

        setMessage('');
        setRating(0);
        fetchReviewData();
        setModalVisible(false);
      } else {
        Toast.show('Failed to submit review', Toast.LONG);
        console.log('Review submit failed. Status:', response.status);
      }
    } catch (err) {
      console.log('Review submit error:', err);
      Toast.show('Something went wrong', Toast.LONG);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = (id: string) => {
    Alert.alert(
      'Remove from Review',
      'Are you sure you want to delete this class from your Review?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await api.deleteReviewData(id);
              if (response.ok) {
                Toast.show('Review Deleted Successfully', Toast.SHORT);
                setReviews([]);
                fetchReviewData();
              } else {
                console.log('Failed to delete data. Status:', response.status);
              }
            } catch (err) {
              console.log('ERROR IN DELETE DATA::', err);
            }
          },
        },
      ],
      {cancelable: true},
    );
  };

  const formatReviewDate = (dateString: string) => {
    const date = new Date(dateString);
    return `Posted on ${date.toLocaleDateString('en-GB')}`;
  };

  const getInitials = (name: string) => {
    if (!name) {return '';}
    const nameParts = name.split(' ');
    if (nameParts.length === 1) {return nameParts[0].slice(0, 2).toUpperCase();}
    return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
  };

  const renderStars = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          marginBottom: '6%',
          marginTop: '2%',
          justifyContent: 'center',
        }}>
        {[1, 2, 3, 4, 5].map(star => (
          <TouchableOpacity key={star} onPress={() => handleRatingPress(star)}>
            <Ionicons
              name={star <= rating ? 'star' : 'star-outline'}
              size={30}
              color={PrimaryColors.ORANGE}
              style={{marginHorizontal: 5}}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <>
      <SafeAreaProvider>
        <SafeAreaView
          style={{
            flex: 1,
            backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE,
          }}
          edges={['left', 'right']}>
          <CurvHeader
            title="Personal Info"
            isBack={true}
            onBackPress={() => {
              navigation.goBack();
            }}
          />
          <ScrollView
            contentContainerStyle={[styles.container]}
            showsVerticalScrollIndicator={false}>
            <View
              style={{
                backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE,
                borderColor: isDarkMode ? '#313131' : '#00000024',
                borderRadius: 10,
                shadowOffset: {width: 2, height: 5},
                shadowOpacity: 0.1,
                shadowRadius: 20,
                elevation: 4,
                paddingHorizontal: '2%',
                paddingVertical: '2%',
                borderWidth: 1,
                paddingBottom: '5%',
              }}>
              <View style={styles.avatarContainer}>
                <View style={styles.avatarWrapper}>
                  <View
                    style={[
                      styles.avatar,
                      {borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC'},
                    ]}>
                    {profileData?.ClassAbout?.profilePhoto && !imageError ? (
                      <Image
                        source={{
                          uri: `${imgBaseUrl}/${profileData.ClassAbout.profilePhoto}`,
                        }}
                        style={styles.avatarImage}
                        resizeMode="cover"
                        onError={() => setImageError(true)}
                      />
                    ) : (
                      <DefaultClassesLogo
                        firstName={profileData?.firstName}
                        lastName={profileData?.lastName}
                      />
                    )}
                  </View>
                </View>
                <View style={styles.textWrapper}>
                  <Text
                    style={[
                      styles.nameText,
                      {
                        color: isDarkMode
                          ? PrimaryColors.WHITE
                          : PrimaryColors.BLACK,
                      },
                    ]}>
                    {profileData?.firstName} {profileData?.lastName}
                  </Text>
                  <Text style={[styles.subTitle, {color: '#6e6e6e'}]}>
                    {formatArrayToString(
                      profileData?.tuitionClasses?.[0]?.coachingType,
                    )}
                  </Text>
                  <View style={styles.ratingContainer}>
                    <View
                      style={{
                        flexDirection: 'row',
                        backgroundColor: '#FFEBF0',
                        padding: 2,
                        borderRadius: 6,
                      }}>
                      <MaterialIcons name="star" size={16} color="#FFFF00" />
                      <Text
                        style={[
                          styles.ratingText,
                          {color: isDarkMode ? '#A3A3B9' : PrimaryColors.BLACK},
                        ]}>
                        {avgRating.toFixed(1)}
                      </Text>
                    </View>
                    <Text
                      style={[
                        styles.reviewCount,
                        {color: isDarkMode ? '#A3A3B9' : '#6E6E6E'},
                      ]}>
                      ({totalReview} reviews)
                    </Text>
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.heartWrapper}
                  onPress={SaveToList}>
                  {isDarkMode ? (
                    <Feather name="heart" size={22} color="#FFFFFF" />
                  ) : (
                    <Feather name="heart" size={22} color="#000000" />
                  )}
                </TouchableOpacity>
              </View>

              <View style={{paddingHorizontal: '2%'}}>
                <Text
                  style={[
                    styles.sectionTitle,
                    {
                      color: isDarkMode
                        ? PrimaryColors.WHITE
                        : PrimaryColors.BLACK,
                    },
                  ]}>
                  About{' '}
                  <Text
                    style={[
                      styles.sectionTitle,
                      {
                        color: '#FF914D',
                      },
                    ]}>
                    Tutor
                  </Text>
                </Text>
                <View>
                  <TouchableOpacity
                    onPress={() => setShowFullBio(prev => !prev)}>
                    <Text style={styles.bodyText}>
                      {showFullBio
                        ? profileData?.ClassAbout?.tutorBio
                        : getTrimmedBio(
                            profileData?.ClassAbout?.tutorBio || '',
                          )}
                      {profileData?.ClassAbout?.tutorBio?.split(/\s+/).length >
                        50 && !showFullBio ? (
                        <Text style={{color: '#FF8C00'}}> more</Text>
                      ) : null}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <View style={styles.tabContainer}>
              <TouchableOpacity
                style={[
                  styles.tabWrapper,
                  activeTab === 'Education'
                    ? styles.activeTab
                    : styles.inactivetab,
                ]}
                onPress={() => setActiveTab('Education')}>
                <Text
                numberOfLines={1}
                  style={[
                    styles.tabText,
                    activeTab === 'Education' ? styles.activeTabText : null,
                  ]}>
                  Education
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.tabWrapper,
                  activeTab === 'Work Experience'
                    ? styles.activeTab
                    : styles.inactivetab,
                ]}
                onPress={() => setActiveTab('Work Experience')}>
                <Text
                  numberOfLines={1}
                  style={[
                    styles.tabText,
                    activeTab === 'Work Experience'
                      ? styles.activeTabText
                      : null,
                  ]}>
                  Work Experience
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.tabWrapper,
                  activeTab === 'Achievement'
                    ? styles.activeTab
                    : styles.inactivetab,
                ]}
                onPress={() => setActiveTab('Achievement')}>
                <Text
                numberOfLines={1}
                  style={[
                    styles.tabText,
                    activeTab === 'Achievement' ? styles.activeTabText : null,
                  ]}>
                  Achievement
                </Text>
              </TouchableOpacity>
            </View>

            {activeTab === 'Education' && (
              <View>
                {profileData?.education?.length > 0 ? (
                  profileData.education.map((item: any, index: number) => (
                    <View
                      key={index}
                      style={[
                        styles.educationItem,
                        index < profileData.education.length - 1
                          ? styles.itemWithBorder
                          : null,
                      ]}>
                      <Text
                        style={[
                          styles.boldText,
                          {
                            color: isDarkMode
                              ? PrimaryColors.WHITE
                              : PrimaryColors.BLACK,
                          },
                        ]}>
                        {item.degree || 'N/A'}
                      </Text>
                      <Text style={styles.subText}>
                        {item.university || 'N/A'}
                      </Text>
                      <Text style={styles.subText}>
                        Passout Year: {item.passoutYear || 'N/A'}
                      </Text>
                    </View>
                  ))
                ) : (
                  <Text style={styles.subText}>No education data</Text>
                )}
              </View>
            )}
            {activeTab === 'Work Experience' && (
              <View>
                {profileData?.experience?.length > 0 ? (
                  profileData.experience.map((item: any, index: number) => (
                    <View
                      key={index}
                      style={[
                        styles.educationItem,
                        index < profileData.experience.length - 1
                          ? styles.itemWithBorder
                          : null,
                      ]}>
                      <Text
                        style={[
                          styles.boldText,
                          {
                            color: isDarkMode
                              ? PrimaryColors.WHITE
                              : PrimaryColors.BLACK,
                          },
                        ]}>
                        {item.title || 'N/A'}
                      </Text>
                      <Text style={styles.subText}>
                        From:{' '}
                        {item.from
                          ? new Date(item.from).toLocaleDateString('en-GB')
                          : 'N/A'}
                      </Text>
                      <Text style={styles.subText}>
                        To:{' '}
                        {item.to
                          ? new Date(item.to).toLocaleDateString('en-GB')
                          : 'N/A'}
                      </Text>
                    </View>
                  ))
                ) : (
                  <Text style={styles.subText}>No work experience</Text>
                )}
              </View>
            )}
            {activeTab === 'Achievement' && (
              <View>
                {profileData?.certificates?.length > 0 ? (
                  profileData.certificates.map((item: any, index: number) => (
                    <View
                      key={index}
                      style={[
                        styles.educationItem,
                        index < profileData.certificates.length - 1
                          ? styles.itemWithBorder
                          : null,
                      ]}>
                      <Text style={styles.subText}>
                        Title: {item.title || 'N/A'}
                      </Text>
                    </View>
                  ))
                ) : (
                  <Text style={styles.subText}>No certificates</Text>
                )}
              </View>
            )}

            <View style={{flexDirection: 'row', marginTop: 8}}>
              <Text
                style={[
                  styles.sectionTitle,
                  {
                    color: isDarkMode
                      ? PrimaryColors.WHITE
                      : PrimaryColors.BLACK,
                    marginRight: 6,
                  },
                ]}>
                Other
              </Text>
              <Text style={[styles.sectionTitle, {color: '#FF8C00'}]}>
                Details
              </Text>
            </View>
            {profileData?.tuitionClasses?.length > 0 &&
              (() => {
                const item = profileData.tuitionClasses[0];
                return (
                  <View style={{paddingVertical: 10}}>
                    <Text style={styles.subText}>Type: {item.education}</Text>
                    <Text style={styles.subText}>
                      Board Type: {formatArrayToString(item.boardType)}
                    </Text>
                    <Text style={styles.subText}>
                      Expertise: {formatArrayToString(item.subject)}
                    </Text>
                    <Text style={styles.subText}>
                      Medium: {formatArrayToString(item.medium)}
                    </Text>
                    <Text style={styles.subText}>
                      Section: {formatArrayToString(item.section)}
                    </Text>
                    <Text style={styles.subText}>
                      Coaching Type: {formatArrayToString(item.coachingType)}
                    </Text>
                  </View>
                );
              })()}

            <Text
              style={[styles.sectionTitle, {color: '#FF914D', marginTop: 8}]}>
              Reviews
            </Text>
            <View style={styles.reviewContainer}>
              {reviews.length > 0 ? (
                reviews.map((review: any) => (
                  <View
                    key={review.id}
                    style={[
                      styles.reviewItem,
                      {
                        backgroundColor: isDarkMode
                          ? '#313131'
                          : PrimaryColors.WHITE,
                      },
                    ]}>
                    <View
                      style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '15%',
                        height: 70,
                      }}>
                      <View
                        style={[
                          {
                            borderWidth: 1,
                            backgroundColor: PrimaryColors.WHITE,
                          },
                          styles.reviewAvatar,
                        ]}>
                        <Text style={{color: '#FF914D', fontSize: 18}}>
                          {getInitials(review.studentName)}
                        </Text>
                      </View>
                    </View>
                    <View style={{justifyContent: 'center', width: '82%'}}>
                      <Text
                        style={{
                          fontSize: 11,
                          fontWeight: 'medium',
                          color: isDarkMode
                            ? PrimaryColors.WHITE
                            : PrimaryColors.BLACK,
                          marginBottom: 2,
                        }}>
                        {review.studentName || 'Anonymous'}
                      </Text>
                      <View style={styles.reviewStars}>
                        {[...Array(5)].map((_, i) => (
                          <MaterialIcons
                            key={i}
                            name={i < review.rating ? 'star' : 'star-border'}
                            size={10}
                            color="#FFD700"
                          />
                        ))}
                      </View>
                      <View>
                        <Text
                          style={{
                            fontSize: 10,
                            color: isDarkMode
                              ? PrimaryColors.WHITE
                              : PrimaryColors.BLACK,
                          }}>
                          {review.message}
                        </Text>
                      </View>
                      <View>
                        <Text
                          style={{
                            fontSize: 7,
                            color: '#C4CEDE',
                            textAlign: 'right',
                          }}>
                          {formatReviewDate(review.createdAt)}
                        </Text>
                      </View>
                    </View>
                  </View>
                ))
              ) : (
                <Text style={styles.subText}>No reviews available</Text>
              )}
            </View>
          </ScrollView>
          <View style={[styles.bottomButtons]}>
            <TouchableOpacity
              style={[
                styles.button,
                {borderRightWidth: 2, borderColor: PrimaryColors.WHITE},
              ]}>
              <View style={{flexDirection: 'row'}}>
                <Image
                  resizeMode="contain"
                  source={IMAGE_CONSTANT.COMMENT}
                  style={{width: 25, height: 30, marginRight: 6}}
                />
                <Text style={styles.buttonText}>Send Message</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button]}
              onPress={() => setModalVisible(true)}>
              <View style={{flexDirection: 'row'}}>
                <Image
                  resizeMode="contain"
                  source={IMAGE_CONSTANT.FEEDBACK}
                  style={{width: 30, height: 25, marginRight: 6}}
                />
                <Text style={styles.buttonText}>Reviews</Text>
              </View>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </SafeAreaProvider>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <View style={[styles.modalOverlay]}>
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: isDarkMode
                  ? PrimaryColors.BLACK
                  : PrimaryColors.WHITE,
                borderWidth:0.5,
                borderColor: isDarkMode ? PrimaryColors.BLACK : '#CCCCCC',
              },
            ]}>
            <View style={styles.modalHeader}>
              <View style={{width: '90%', paddingLeft: '5%'}}>
                <Text
                  style={[
                    styles.sectionTitle,
                    {
                      color: isDarkMode
                        ? PrimaryColors.WHITE
                        : PrimaryColors.BLACK,
                      textAlign: 'center',
                      fontSize: 22,
                    },
                  ]}>
                  Add Review
                </Text>
              </View>
              <View style={{width: '5%'}}>
                <TouchableOpacity onPress={() => setModalVisible(false)}>
                  <View
                    style={[
                      styles.closeIconContainer,
                      {
                        borderWidth: 3,
                        borderColor: isDarkMode ? '#6E6E6E' : '#6E6E6E',
                      },
                    ]}>
                    <Ionicons name="close" size={24} color={'#6E6E6E'} />
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            <View style={{justifyContent: 'center', alignItems: 'center'}}>
              <View style={styles.avatarWrapper}>
                <View
                  style={[
                    styles.avatar,
                    {borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC'},
                  ]}>
                  {profileData?.ClassAbout?.profilePhoto && !imageError ? (
                    <Image
                      source={{
                        uri: `${imgBaseUrl}/${profileData.ClassAbout.profilePhoto}`,
                      }}
                      style={styles.avatarImage}
                      resizeMode="cover"
                      onError={() => setImageError(true)}
                    />
                  ) : (
                    <DefaultClassesLogo
                      firstName={profileData?.firstName}
                      lastName={profileData?.lastName}
                    />
                  )}
                </View>
              </View>
              <View style={styles.textWrapper}>
                <Text
                  style={[
                    styles.nameText,
                    {
                      color: isDarkMode
                        ? PrimaryColors.WHITE
                        : PrimaryColors.BLACK,
                      fontSize: 26,
                      fontWeight: 'medium',
                      paddingTop: 6,
                    },
                  ]}>
                  {profileData?.firstName} {profileData?.lastName}
                </Text>
                <View style={{justifyContent: 'center', alignItems: 'center'}}>
                  <View style={styles.ratingContainer}>
                    <View
                      style={{
                        flexDirection: 'row',
                        backgroundColor: '#FFEBF0',
                        padding: 2,
                        borderRadius: 6,
                      }}>
                      <MaterialIcons name="star" size={16} color="#FFFF00" />
                      <Text
                        style={[
                          styles.ratingText,
                          {color: '#A3A3B9', fontSize: 16},
                        ]}>
                        {avgRating.toFixed(1)}
                      </Text>
                    </View>
                    <Text
                      style={[
                        styles.reviewCount,
                        {
                          color: isDarkMode ? '#A3A3B9' : '#6E6E6E',
                          fontSize: 16,
                        },
                      ]}>
                      ({totalReview} reviews)
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            <View style={{paddingTop: '4%'}}>
              <Text
                style={{
                  fontSize: 24,
                  fontWeight: 'medium',
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                  textAlign: 'center',
                }}>
                How was your experience?
              </Text>
              <View
                style={[
                  styles.testimonialCard,
                  {
                    borderColor: isDarkMode ? PrimaryColors.BLACK : '#CCCCCC',
                    backgroundColor: isDarkMode
                      ? PrimaryColors.BLACK
                      : PrimaryColors.WHITE,
                    paddingTop: 5,
                  },
                ]}>
                {renderStars()}
                {error.rating && (
                  <Text
                    style={{
                      color: PrimaryColors.RED,
                      fontSize: 12,
                      marginBottom: '4%',
                    }}>
                    {strings.Review.MINIMUMRATING}
                  </Text>
                )}

                <Text
                numberOfLines={1}
                  style={[
                    styles.labelText,
                    {
                      color: isDarkMode
                        ? PrimaryColors.WHITE
                        : PrimaryColors.BLACK,
                      backgroundColor: isDarkMode
                        ? PrimaryColors.BLACK
                        : PrimaryColors.WHITE,
                    },
                  ]}>
                  {strings.Review.WRITEREVIEWS}
                </Text>
                <TextInput
                  placeholder={strings.Review.SHAREYOUREXPERIENCE}
                  placeholderTextColor={isDarkMode ? '#A9A9A9' : '#CDCEDE'}
                  value={message}
                  onChangeText={text => {
                    setMessage(text);
                    if (text.trim()) {
                      setError(prev => ({...prev, message: false}));
                    }
                  }}
                  multiline
                  style={[
                    styles.textInput,
                    {
                      color: isDarkMode ? '#A9A9A9' : '#CDCEDE',
                      backgroundColor: isDarkMode
                        ? PrimaryColors.BLACK
                        : PrimaryColors.WHITE,
                      borderColor: isDarkMode ? '#3E3E3E' : PrimaryColors.BLACK,
                    },
                  ]}
                />

                {error.message && (
                  <Text
                    style={{
                      color: PrimaryColors.RED,
                      fontSize: 12,
                      marginTop: '1%',
                    }}>
                    {strings.Review.MESSAGEREQUIRED}
                  </Text>
                )}

                <View style={{alignItems: 'center', marginTop: '5%'}}>
                  {isLoading ? (
                    <ActivityIndicator
                      size="large"
                      color={PrimaryColors.ORANGE}
                    />
                  ) : (
                    <TouchableOpacity
                      onPress={handleSubmit}
                      style={{
                        backgroundColor: '#FF914D',
                        width: '100%',
                        paddingVertical: 10,
                        borderRadius: 10,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          color: PrimaryColors.WHITE,
                          fontSize: 18,
                          fontWeight: 'medium',
                        }}>
                        Submit
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  avatarContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  avatarWrapper: {
    width: '30%',
    alignItems: 'center',
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    borderWidth: 1,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
  textWrapper: {
    flex: 1,
    marginLeft: 10,
    justifyContent: 'center',
  },
  nameText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  subTitle: {
    fontSize: 14,
    marginVertical: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 4,
    color: '#000',
  },
  reviewCount: {
    fontSize: 14,
    marginLeft: 10,
  },
  heartWrapper: {
    width: '10%',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  bodyText: {
    fontSize: 12,
    color: '#6E6E6E',
    lineHeight: 20,
    letterSpacing: 0.5,
  },
  tabContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
    marginBottom: 10,
  },
  tabWrapper: {
    flex: 1,
    alignItems: 'center',
    paddingBottom: 5,
  },
  tabText: {
    fontSize: 16,
    color: '#6E6E6E',
  },
  activeTab: {
    borderBottomWidth: 1,
    borderBottomColor: '#FF8C00',
  },
  inactivetab: {
    borderBottomWidth: 0.5,
    borderBottomColor: '#4B4A4A',
  },
  activeTabText: {
    color: '#FF8C00',
  },
  educationItem: {
    marginBottom: 15,
    paddingHorizontal: '2%',
  },
  boldText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 3,
  },
  subText: {
    fontSize: 12,
    color: '#A9A9A9',
    letterSpacing: 0.5,
    lineHeight: 22,
  },
  reviewContainer: {
    marginVertical: 12,
    justifyContent: 'center',
  },
  reviewItem: {
    borderRadius: 10,
    marginBottom: 16,
    flexDirection: 'row',
    width: '100%',
    paddingLeft: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.25,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  reviewAvatar: {
    width: 48,
    height: 48,
    borderRadius: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    borderColor: '#C4CEDE',
  },
  reviewStars: {
    flexDirection: 'row',
    marginBottom: 2,
  },
  bottomButtons: {
    flexDirection: 'row',
    paddingVertical: 4,
    backgroundColor: '#FF8C00',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  button: {
    backgroundColor: '#FF8C00',
    paddingVertical: 10,
    paddingHorizontal: 20,
    width: '50%',
    flex: 1,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '500',
  },
  itemWithBorder: {
    borderBottomWidth: 0.5,
    borderBottomColor: '#C7C5C5',
    paddingBottom: 10,
  },
  modalOverlay: {
    flex: 1,

    justifyContent: 'flex-end',
  },
  modalSafeArea: {
    flex: 0,
    margin: 0,
  },
  modalContent: {
    width: '100%',
    maxHeight: SCREEN_HEIGHT * 0.8,
    backgroundColor: PrimaryColors.WHITE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    position: 'absolute',
    bottom: 0,
  },
  closeIconContainer: {
    width: 30,
    height: 30, 
    borderRadius: 20,
    borderWidth: 1,
    justifyContent: 'center', 
    alignItems: 'center', 
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  testimonialCard: {
    width: '100%',
  },
  labelText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 5,
    position: 'relative',
    top: '5%',
    left: '5%',
    zIndex: 1,
    width: '25%',
    backgroundColor: PrimaryColors.WHITE,
    letterSpacing: 0.2,
  },
  textInput: {
    width: '100%',
    borderWidth: 1,
    borderRadius: 16,
    minHeight: 130,
    textAlignVertical: 'top',
    fontSize: 14,
    backgroundColor: PrimaryColors.WHITE,
    paddingTop: 20,
    paddingLeft: '5%',
  },
});

export default Profile;
