/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import strings from '../../../../Utils/LocalizedStrings/LocalizedStrings';
import {IMAGE_CONSTANT, PrimaryColors} from '../../../../Utils/Constants';
import IndexStyle from '../../../../Theme/IndexStyle';
import Button from '../../../../CommonComponents/Button';
import api from '../../../../config/api';
import CommonTextInput from '../../../../CommonComponents/CommonTextInput';
import NavigationHeader from '../../../../CommonComponents/NavigationHeader';
import {useNavigation} from '@react-navigation/native';
import DefaultClassesLogo from '../../../../CommonComponents/DefaultClassesLogo';
import {imgBaseUrl} from '../../../../config/apiUrl';
import {useRoute} from '@react-navigation/native';
import Toast from 'react-native-simple-toast';
import {ScrollView} from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getProfileData } from '../../../../services/classService';
import { getReviewData, submitReviewData } from '../../../../services/reviewService';
import {triggerReviewSubmissionNotification} from '../../../../services/notificationService';

const Review = () => {
  const navigation = useNavigation<any>();
  const {styles} = IndexStyle();
  const [rating, setRating] = useState(0);
  const [message, setMessage] = useState('');
  const [error, setError] = useState({rating: false, message: false});
  const [review, setReview] = useState<any[]>([]);
  const {isDarkMode} = IndexStyle();
  const [profileData, setProfileData] = useState<any>(null);
  const [storedUserData, setStoredUserData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const route = useRoute<any>();
  const {classId, avgRating, totalReview} = route.params;

  const handleRatingPress = (star: number) => {
    setRating(star);
    if (star > 0) {
      setError(prev => ({...prev, rating: false}));
    }
  };

  console.log('ClassID', classId, avgRating, totalReview);

  useEffect(() => {
    getProfileDataHandler();
    fetchReviewtData();
    fetchStoredUserData();
  }, []);

  const getProfileDataHandler = async () => {
    try {
      const data = await getProfileData(classId);
      setProfileData(data);
      console.log('class data', data);
    } catch (err) {
      console.log('Profile fetch error:', err);
    }
  };

  const fetchReviewtData = async (pageNumber = 1) => {
    try {
      const data = await getReviewData(classId, pageNumber, 10);
      setReview(data.reviews || []);
      console.log('PARSED  DATA::', data);
      console.log('Review  DATA::', data.reviews);
    } catch (err) {
      console.log('ERROR IN GET REVIEW DATA::', err);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const fetchStoredUserData = async () => {
    try {
      const userDataString = await AsyncStorage.getItem('userData');
      console.log('Raw AsyncStorage userData:', userDataString);
      if (userDataString) {
        const userData = JSON.parse(userDataString);
        console.log('Parsed userData:', userData);
        setStoredUserData(userData.data);
      } else {
        console.log('No userData found in AsyncStorage');
      }
    } catch (error) {
      console.error('Error retrieving userData from AsyncStorage:', error);
    }
  };

  const handleSubmit = async () => {
    const messageError = !message.trim();
    const ratingError = rating === 0;

    setError({
      message: messageError,
      rating: ratingError,
    });

    if (messageError || ratingError) {
      return;
    }

    const studentId = storedUserData?.user?.id;
    const studentName = `${storedUserData?.user?.firstName} ${storedUserData?.user?.lastName}`;

    const alreadyReviewed = review.some(item => item.studentId === studentId);

    if (alreadyReviewed) {
      Toast.show('You have already submitted a review', Toast.LONG);
      return;
    }

    const addData = {
      rating,
      message,
      classId,
      studentId,
      studentName,
    };

    console.log('Adding REVIEW with:', addData);

    try {
      setIsLoading(true);
      const data = await submitReviewData(addData);
      Toast.show('Review submitted successfully!', Toast.SHORT);

      // Trigger notification for successful review submission
      const className = profileData?.className || 'Class';
      await triggerReviewSubmissionNotification(className, rating);

      setMessage('');
      setRating(0);
      fetchReviewtData();
    } catch (err) {
      console.log('Review submit error:', err);
      Toast.show('Something went wrong', Toast.LONG);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = (id: string) => {
    Alert.alert(
      'Remove from Review',
      'Are you sure you want to delete this class from your Review?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await api.deleteReviewData(id);
              if (response.ok) {
                Toast.show('Review Deleted Successfully', Toast.SHORT);
                setReview([]);
                fetchReviewtData();
              } else {
                console.log('Failed to delete data. Status:', response.status);
              }
            } catch (err) {
              console.log('ERROR IN DELETE DATA::', err);
            }
          },
        },
      ],
      {cancelable: true},
    );
  };

  const renderStars = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          marginVertical: '1%',
          marginBottom: '3%',
          marginTop: '5%',
        }}>
        {[1, 2, 3, 4, 5].map(star => (
          <TouchableOpacity key={star} onPress={() => handleRatingPress(star)}>
            <Ionicons
              name={star <= rating ? 'star' : 'star-outline'}
              size={24}
              color={PrimaryColors.ORANGE}
              style={{marginHorizontal: 2}}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderStaticStars = (value: number) => (
    <View style={{flexDirection: 'row', marginVertical: 5}}>
      {[1, 2, 3, 4, 5].map(star => (
        <Ionicons
          key={star}
          name={star <= value ? 'star' : 'star-outline'}
          size={20}
          color={PrimaryColors.ORANGE}
          style={{marginHorizontal: 1}}
        />
      ))}
    </View>
  );

  return (
    <SafeAreaProvider
      style={{
        backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
      }}>
      <NavigationHeader
        title={strings.Review.REVIEW}
        isBack={true}
        onBackPress={() => navigation.goBack()}
      />

      <SafeAreaView style={{flex: 1}}>
        <View style={{alignItems: 'center'}}>
          <View
            style={[
              localStyles.avatar,
              {borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC'},
            ]}>
            {profileData?.ClassAbout?.profilePhoto ? (
              <Image
                source={{
                  uri: `${imgBaseUrl}/${profileData.ClassAbout.profilePhoto}`,
                }}
                style={localStyles.avatarImage}
                resizeMode="cover"
              />
            ) : (
              <DefaultClassesLogo
                firstName={profileData?.firstName}
                lastName={profileData?.lastName}
              />
            )}
          </View>
          <Text
            style={[
              styles.label,
              {
                fontSize: 22,
                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              },
            ]}>
            {profileData?.firstName} {profileData?.lastName}
          </Text>
          <Text
            style={{
              fontSize: 18,
              color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
            }}>
            {avgRating}/5
          </Text>
        </View>
        <View style={{borderTopLeftRadius: 26,borderTopRightRadius: 26,borderBottomWidth:0,marginTop:8,borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC',overflow:'visible',borderWidth:1,paddingTop:20}}>
        <ScrollView Style={{flexGrow: 1,overflow:'scroll',borderTopLeftRadius: 26,borderTopRightRadius: 26,marginTop:20}}>
          <View
            style={[
              styles.testimonialCard,
              // styles.lightBackgroundShadow,
              {
                borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC',
                backgroundColor: isDarkMode
                  ? PrimaryColors.BLACK
                  : PrimaryColors.WHITE,
                marginTop: '10%',
              },
            ]}>
            {renderStars()}
            {error.rating && (
              <Text
                style={{
                  color: PrimaryColors.RED,
                  fontSize: 12,
                  marginBottom: '4%',
                }}>
                {strings.Review.MINIMUMRATING}
              </Text>
            )}

            <CommonTextInput
              label={strings.Review.YOURMESSAGE}
              placeholder={strings.Review.SHAREYOUREXPERIENCE}
              value={message}
              onChangeText={text => {
                setMessage(text);
                if (text.trim()) {
                  setError(prev => ({...prev, message: false}));
                }
              }}
              multiline
              style={{width: '100%'}}
            />

            {error.message && (
              <Text
                style={{
                  color: PrimaryColors.RED,
                  fontSize: 12,
                  marginTop: '1%',
                }}>
                {strings.Review.MESSAGEREQUIRED}
              </Text>
            )}

            <View style={{alignItems: 'center', marginBottom: '5%'}}>
              {isLoading ? (
                <ActivityIndicator size="large" color={PrimaryColors.ORANGE} />
              ) : (
                <Button
                  title={strings.Review.SUBMITREVIEW}
                  onPress={handleSubmit}
                />
              )}
            </View>

            {review.map(item => (
              <View
                key={item.id}
                style={[
                  localStyles.card,
                  // styles.lightBackgroundShadow,
                  {
                    borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC',
                    borderWidth:1,
                    padding: '5%',
                    marginVertical: 10,
                    backgroundColor: isDarkMode
                      ? '#1b1b1b'
                      : PrimaryColors.WHITE,
                    marginLeft: 12,
                    marginRight: 12,
                  },
                ]}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <View style={{width: '95%'}}>
                    <Text
                      style={[
                        localStyles.title,
                        {
                          fontWeight: 'bold',
                          color: isDarkMode
                            ? PrimaryColors.WHITE
                            : PrimaryColors.BLACK,
                        },
                      ]}>
                      {item.studentName || 'N/A'}
                    </Text>
                  </View>
                  <View style={{marginTop: '2%'}}>
                    {item.studentId === storedUserData?.user?.id && (
                      <TouchableOpacity onPress={() => handleDelete(item.id)}>
                        <Image
                          resizeMode="contain"
                          source={IMAGE_CONSTANT.DELETE}
                          style={{width: 20, height: 20}}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>

                {renderStaticStars(item.rating || 0)}
                <Text
                  style={[
                    localStyles.label,
                    {
                      fontWeight: 'bold',
                      color: isDarkMode
                        ? PrimaryColors.WHITE
                        : PrimaryColors.BLACK,
                    },
                  ]}>
                  {item.message || 'N/A'}
                </Text>
                <Text
                  style={[
                    localStyles.label,
                    {
                      color: isDarkMode
                        ? PrimaryColors.WHITE
                        : PrimaryColors.BLACK,
                    },
                  ]}>
                  {strings.Review.POSTEDON}:{' '}
                  {formatDate(item.createdAt) || 'N/A'}
                </Text>
              </View>
            ))}
          </View>
        </ScrollView>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default Review;

const localStyles = StyleSheet.create({
  testimonialCard: {
    backgroundColor: '#fff',
    width: '100%',
    borderRadius: 10,
    padding: 15,
    marginTop: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  label: {
    marginTop: 10,
    fontWeight: '600',
  },
  textInput: {
    borderWidth: 1,
    borderColor: PrimaryColors.GRAYSHADOW,
    borderRadius: 8,
    height: 100,
    padding: 10,
    textAlignVertical: 'top',
    marginTop: 5,
  },
  card: {
    borderRadius: 12,
    marginTop: 20,
    alignItems: 'flex-start',
    paddingTop: 10,
  },
  avatar: {
    width: 110,
    height: 110,
    borderRadius: 55,
    overflow: 'hidden',
    marginBottom: 10,
    borderWidth: 1,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
});
