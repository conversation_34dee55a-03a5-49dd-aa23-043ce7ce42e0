import AsyncStorage from '@react-native-async-storage/async-storage';
import {apiUrl, ExamBaseUrl} from './apiUrl';
import { use } from 'react';

const getCommonHeaders = () => ({
  'Content-Type': 'application/json',
  Accept: '*/*',
  'client-type': 'uest-mobile-app',
});

export default {
  TutorRegistration: {
    tutorRegister: (data: any) => {
      let url = `${apiUrl}/auth-client/register`,
        opt = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: '*/*',
            'client-type': 'mobile-app',
          },
          body: JSON.stringify(data),
        };
      console.log('IN Register tutor API FUNCTION::');

      return fetch(url, opt);
    },
  },
  TutorLogin: {
    tutorLogin: function (data: any) {
      let url = `${apiUrl}/auth-client/login`,
        opt = {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
            Accept: '*/*',
            'client-type': 'mobile-app',
          },
          body: JSON.stringify(data),
        };
      console.log('IN LOGIN API FUNCTION::');

      return fetch(url, opt);
    },
  },
  TutorProfile: {
    TutorAbout: {
      updateAbout: async function (data: any) {
        const token = await AsyncStorage.getItem('token');
        console.log('hello from token', token);

        let url = `${apiUrl}/classes-profile/about`,
          opt = {
            method: 'POST',
            headers: {
              'content-type': 'application/json',
              Accept: '*/*',
              'client-type': 'mobile-app',
              cookie: `client_jwt=${token}`,
            },
            body: JSON.stringify(data),
          };
        console.log('UPDATE PROFILE API FUNCTION::');

        return fetch(url, opt);
      },
    },
    TutorDescription: {
      updateAbout: async function (data: any) {
        const token = await AsyncStorage.getItem('token');
        console.log(token);

        let url = `${apiUrl}/classes-profile/description`,
          opt = {
            method: 'POST',
            headers: {
              'content-type': 'application/json',
              Accept: '*/*',
              'client-type': 'mobile-app',
              cookie: `client_jwt=${token}`,
            },
            body: JSON.stringify(data),
          };
        console.log('UPDATE DESCRIPTION API FUNCTION::');

        return fetch(url, opt);
      },
    },
    TutorPhotoAndLogo: {
      updatePhotoAndLogo: async function (
        photoUri: string | null,
        logoUri: string | null,
      ) {
        const token = await AsyncStorage.getItem('token');
        console.log('Token', token);
        console.log('hello');

        console.log('Photo uri', photoUri);
        console.log('Photo uri', logoUri);

        let url = `${apiUrl}/classes-profile/images`;
        let formData = new FormData();
        if (photoUri) {
          formData.append('profilePhoto', {
            uri: photoUri,
            name: 'profile_photo.jpg',
            type: 'image/jpeg',
          });
        }
        if (logoUri) {
          console.log('Photo uri', logoUri);

          formData.append('classesLogo', {
            uri: logoUri,
            name: 'class_logo.jpg',
            type: 'image/jpeg',
          });
        }
        const opt = {
          method: 'POST',
          headers: {
            Accept: '*/*',
            'client-type': 'mobile-app',
            cookie: `client_jwt=${token}`,
          },
          body: formData as any,
        };
        console.log('UPDATE PROFILE PHOTO AND LOGO API FUNCTION::');

        return fetch(url, opt);
      },
    },
    ThoughtApi: {
      getThoughts: async function (classId: string, page = 1, limit = 10) {
        const token = await AsyncStorage.getItem('token');

        const url = `${apiUrl}/classes-thought?classId=${classId}&page=${page}&limit=${limit}`;
        const opt = {
          method: 'GET',
          headers: {
            'content-type': 'application/json',
            Accept: '*/*',
            'client-type': 'mobile-app',
            cookie: `client_jwt=${token}`,
          },
        };

        console.log('Fetching thoughts with classId:', classId);
        return fetch(url, opt);
      },
      saveThought: async function (data: {classId: string; thoughts: string}) {
        const token = await AsyncStorage.getItem('token');

        let url = `${apiUrl}/classes-thought`,
          opt = {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Accept: '*/*',
              'client-type': 'mobile-app',
              cookie: `client_jwt=${token}`,
            },
            body: JSON.stringify(data),
          };

        console.log('IN ADD THOUGHT API FUNCTION::', data);

        return fetch(url, opt);
      },
      deleteThought: async function (thoughtId: string) {
        const token = await AsyncStorage.getItem('token');

        const url = `${apiUrl}/classes-thought/${thoughtId}`;

        const opt = {
          method: 'DELETE',
          headers: {
            'content-type': 'application/json',
            Accept: '*/*',
            'client-type': 'mobile-app',
            cookie: `client_jwt=${token}`,
          },
        };

        console.log('IN DELETE THOUGHT API FUNCTION::', url);

        return fetch(url, opt);
      },
      updateThought: async function (thoughtId: string, thoughts: string) {
        const token = await AsyncStorage.getItem('token');

        const url = `${apiUrl}/classes-thought/${thoughtId}`;

        const opt = {
          method: 'PUT',
          headers: {
            'content-type': 'application/json',
            Accept: '*/*',
            'client-type': 'mobile-app',
            cookie: `client_jwt=${token}`,
          },
          body: JSON.stringify({thoughts}),
        };

        console.log('IN UPDATE THOUGHT API FUNCTION::', url, 'BODY:', opt.body);

        return fetch(url, opt);
      },
    },
    TutorEducation: {
      updateEducation: async function (
        educationData: any[],
        files: {uri: string; name: string; type: string}[],
        noDegree: boolean,
      ) {
        const token = await AsyncStorage.getItem('token');
        const url = `${apiUrl}/classes-profile/education`;
        let formData = new FormData();
        formData.append('noDegree', noDegree ? 'true' : 'false');

        if (!noDegree && educationData.length > 0) {
          formData.append('education', JSON.stringify(educationData));

          files.forEach((file, index) => {
            if (file && file.uri) {
              const fileName = file.name || `certificate_${index}`;
              const fileType =
                file.type ||
                (fileName.endsWith('.pdf') ? 'application/pdf' : 'image/jpeg');

              formData.append('files', {
                uri: file.uri,
                name: fileName,
                type: fileType,
              });
            }
          });
        }
        const opt = {
          method: 'POST',
          headers: {
            Accept: '*/*',
            'client-type': 'mobile-app',
            cookie: `client_jwt=${token}`,
          },
          body: formData as any,
        };

        console.log('UPDATE EDUCATION API FUNCTION::', {
          noDegree,
          educationData,
          files,
        });

        return fetch(url, opt);
      },
    },
    TutorCertificate: {
      uploadCertificates: async function (
        noCertificates: boolean,
        certificates: {title: string; file: any}[],
      ) {
        const token = await AsyncStorage.getItem('token');
        const url = `${apiUrl}/classes-profile/certificates`;

        let formData = new FormData();

        formData.append('noCertificates', noCertificates ? 'true' : 'false');

        if (!noCertificates && certificates.length > 0) {
          const certData = certificates.map(cert => ({title: cert.title}));
          formData.append('certificates', JSON.stringify(certData));

          certificates.forEach((cert, index) => {
            if (cert.file && cert.file.uri) {
              formData.append('files', {
                uri: cert.file.uri,
                name: cert.file.name || `certificate_${index}.pdf`,
                type: cert.file.type || 'application/pdf',
              });
            }
          });
        }

        const opt = {
          method: 'POST',
          headers: {
            Accept: '*/*',
            'Content-Type': 'multipart/form-data',
            'client-type': 'mobile-app',
            cookie: `client_jwt=${token}`,
          },
          body: formData,
        };

        return fetch(url, opt);
      },
    },
    TutorTuitionClass: {
      saveTutionList: async (data: any) => {
        const token = await AsyncStorage.getItem('token');
        console.log('hello from the save tuition api');

        return fetch(`${apiUrl}/classes-profile/tuition-classes`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: '*/*',
            'client-type': 'mobile-app',
            cookie: `client_jwt=${token}`,
          },
          body: JSON.stringify(data),
        });
      },
      deleteTutionClass: async function (itemId: string, classId: string) {
        const token = await AsyncStorage.getItem('token');
        const url = `${apiUrl}/classes-profile/tuition-class/${itemId}`;
        console.log('itemId', itemId);
        console.log('classId', classId);

        const opt = {
          method: 'DELETE',
          headers: {
            Accept: '*/*',
            'Content-Type': 'application/json',
            'client-type': 'mobile-app',
            cookie: `client_jwt=${token}`,
          },
          body: JSON.stringify({
            classId: classId,
          }),
        };

        return fetch(url, opt);
      },
    },
  },
  Student: {
    studentRegister: (data: any) => {
      let url = `${apiUrl}/student/register`,
        opt = {
          method: 'POST',
          headers: getCommonHeaders(),
          body: JSON.stringify(data),
        };
      console.log('IN Register Student API FUNCTION::');

      return fetch(url, opt);
    },
    googleLogin: (data: any) => {
      let url = `${apiUrl}/student/google-auth`;
      let opt = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: '*/*',
          'client-type': 'mobile-app',
        },
        body: JSON.stringify(data),
      };
      console.log('IN google login Student API FUNCTION::');

      return fetch(url, opt);
    },
  },

  GetClassList: {
    getClassList: function (page = 1, limit = 9) {
      let url = `${apiUrl}/classes/approved-tutors?page=${page}&limit=${limit}&sortByRating=true&sortByReviewCount=true`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
          },
        };
      console.log('classlist fetch with', limit);

      console.log('IN CLASS LIST API FUNCTION::');

      return fetch(url, opt);
    },
  },
  GetClassCategoryList: {
    getClassCategoryList: function () {
      let url = `${apiUrl}/constant`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
          },
        };
      console.log('IN CLASS CATEGORY LIST API FUNCTION::');

      return fetch(url, opt);
    },
  },
  GetExamList: {
    getExamList: async function (page = 1, limit = 9,applicantId?: string) {
      let url = `${ExamBaseUrl}exams?page=${page}&limit=${limit}${applicantId ? `&applicantId=${applicantId}` : ''}`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
            'client-type': 'mobile-app',
            'Content-Type': 'application/json',
          },
        };
      console.log('IN Exam LIST API FUNCTION::');
      return fetch(url, opt);
    },
  },
  GetProfileData: {
    getProfileData: function (classId: string) {
      let url = `${apiUrl}/classes/details/${classId}`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
          },
        };
      console.log('IN PROFILE DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  GetResultData: {
    getResultData: function (examId: string) {
      let url = `${apiUrl}/ranking/${examId}`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
          },
        };
      console.log('IN RESULT DATA API FUNCTION::');

      return fetch(url, opt);
    },
  },
  UpdateAboutData: {
    updateAboutData: function (classId: string) {
      let url = `${apiUrl}/classes-profile/about/${classId}`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
          },
        };
      console.log('IN RESULT DATA API FUNCTION::');

      return fetch(url, opt);
    },
  },
  GetReviewData: {
    getReviewData: function (classId: string, page = 1, limit = 1) {
      let url = `${apiUrl}/reviews/class/${classId}?page=${page}&limit=${limit}`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
          },
        };
      console.log('IN REVIEW DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  ForgotPassword: {
    forgotPassword: function (data: any) {
      console.log('FORGOT PASSWORD EMAIL:::', data);

      let url = `${apiUrl}/auth-client/forgot-password`,
        opt = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: '*/*',
          },
          body: JSON.stringify(data),
        };
      console.log('IN RESULT FORGOT PASSWORD API FUNCTION::');

      return fetch(url, opt);
    },
  },
  deleteReviewData: async function (reviewId: string) {
    const token = await AsyncStorage.getItem('token');
    console.log(token);
    let url = `${apiUrl}/reviews/${reviewId}`,
      opt = {
        method: 'DELETE',
        headers: {
          Accept: '*/*',
          'client-type': 'mobile-app',
          Authorization: 'Bearer ' + token,
        },
        // body:JSON.stringify(data)
      };
    console.log('IN REVIEW DATA API FUNCTION::');
    return fetch(url, opt);
  },
  SubmitReviewData: {
    submitReviewData: async function (data: any) {
      const token = await AsyncStorage.getItem('token');
      console.log(token);
      let url = `${apiUrl}/reviews/`,
        opt = {
          method: 'POST',
          headers: {
            Accept: '*/*',
            'Content-Type': 'application/json',
            'client-type': 'mobile-app',
            Authorization: 'Bearer ' + token,
          },
          body: JSON.stringify(data),
        };
      console.log('IN REVIEW DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  GetWhislistData: {
    getWhislistData: async function (page = 1, limit = 10) {
      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/student-wishlist?page=${page}&limit=${limit}`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
            Authorization: 'Bearer ' + token,
          },
        };
      console.log('IN WHISLIST DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  DeleteWhislistData: {
    deleteWhislistData: async function (id: string) {
      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/student-wishlist/${id}`,
        opt = {
          method: 'DELETE',
          headers: {
            Accept: '*/*',
            Authorization: 'Bearer ' + token,
          },
        };
      console.log('IN DELETEWHISLIST DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  AddWishList: {
    addWishlist: async function (data: any) {
      console.log('WISHLIST:::', data);
      const token = await AsyncStorage.getItem('token');
      console.log('TOKEN:::::', token);
      let url = `${apiUrl}/student-wishlist`,
        opt = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: '*/*',
            Authorization: 'Bearer ' + token,
          },
          body: JSON.stringify(data),
        };
      console.log('IN WISHLIST API');

      return fetch(url, opt);
    },
  },
  GetBlogData: {
    getBlogData: async function (page = 1, limit = 10) {
      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/blogs/my-blogs?page=${page}&limit=${limit}`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
            Cookie: 'client_jwt=' + token,
          },
        };
      console.log('IN BLOG DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  AddBlogData: {
    addBlogData: async function (data: any) {
      console.log('BLOG ADD DATA:::', data);

      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/blogs`,
        opt = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: '*/*',
            Cookie: 'client_jwt=' + token,
          },
          body: JSON.stringify(data),
        };
      console.log('IN ADD BLOG DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  EditBlogData: {
    editBlogData: async function (id: string, data: any) {
      console.log('BLOG UPDATE DATA:::', data, 'ID::', id);
      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/blogs/${id}`,
        opt = {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Accept: '*/*',
            Cookie: 'client_jwt=' + token,
          },
          body: JSON.stringify(data),
        };
      console.log('IN EDIT BLOG DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  GetTestmoinalData: {
    getTestmoinalData: function (classId: string) {
      let url = `${apiUrl}/testimonials/class/${classId}`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
          },
        };
      console.log('IN TESTMOINAL DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  DeleteTestmoinalData: {
    deleteTestmoinalData: async function (id: string) {
      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/testimonials/${id}`,
        opt = {
          method: 'DELETE',
          headers: {
            Accept: '*/*',
            Cookie: token,
          },
        };
      console.log('IN DELETE TESTMOINAL DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  AddTestmoinalData: {
    addTestmoinalData: async function (data: any) {
      console.log('TESTMOINAL ADD DATA:::', data);
      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/testimonials`,
        opt = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: '*/*',
            Cookie: token,
          },
          body: JSON.stringify(data),
        };
      console.log('IN ADD TESTMOINAL DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  GetStudentProfileData: {
    getStudentProfileData: async function () {
      const Token = await AsyncStorage.getItem('token');
      console.log('Token', Token);
      let url = `${apiUrl}/student-profile/all-data`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
            Authorization: 'Bearer ' + Token,
            Cokkie: 'client_jwt ' + Token,
          },
        };
      console.log('IN STUDENT PROFILE DATA API FUNCTION::');
      return fetch(url, opt);
    },
  },
  GetClassesAvgRating: {
    getStudentAvgRating: async function (classsId: any) {
      let url = `${apiUrl}/reviews/average/${classsId}`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
          },
        };
      console.log('IN STUDENT PROFILE Review Rating API FUNCTION::');
      return fetch(url, opt);
    },
  },
  GetClassesTotalReview: {
    getClassesTotalReview: async function (classsId: any, page = 1, limit = 1) {
      let url = `${apiUrl}/reviews/class/${classsId}/?page={${page}}&limit={${limit}}`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
          },
        };
      console.log('IN CLASSES PROFILE Review Rating API FUNCTION::');
      return fetch(url, opt);
    },
  },
  UpdateStudentProfileData: {
    updateStudentProfileData: async function (data: any) {
      console.log('STUDENT PROFILE DATA:::', data);
      const token = await AsyncStorage.getItem('token');
      console.log(token);
      let url = `${apiUrl}/student-profile/combined`,
        opt = {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Accept: '*/*',
            'client-type': 'mobile-app',
            Authorization: 'Bearer ' + token,
          },
          body: JSON.stringify(data),
        };
      console.log('UPDATE STUDENT PROFILE API FUNCTION');

      return fetch(url, opt);
    },
  },
  StudentCoin: {
    getTotalCoins: async function () {
      const token = await AsyncStorage.getItem('token');

      const url = `${apiUrl}/coins/get-total-coins/student`;
      const opt = {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Accept: '*/*',
          'client-type': 'mobile-app',
          Authorization: `Bearer ${token}`,
        },
      };
      console.log('Hello from get totalcoins api');

      return fetch(url, opt);
    },
    getTransactionHistory: async function () {
      const token = await AsyncStorage.getItem('token');
      const url = `${apiUrl}/coins/transaction-history/student`;
      const opt = {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Accept: '*/*',
          'client-type': 'mobile-app',
          Authorization: `Bearer ${token}`,
        },
      };
      return fetch(url, opt);
    },
    createOrder: async function (amount: any) {
      const token = await AsyncStorage.getItem('token');
      const url = `${apiUrl}/coins/create-order/`;

      const opt = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: '*/*',
          'client-type': 'mobile-app',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({amount}),
      };

      return fetch(url, opt);
    },
    verifyPayment: async function ({
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      amount,
    }: any) {
      const token = await AsyncStorage.getItem('token');
      const url = `${apiUrl}/coins/verify`;
      console.log(
        'inside the verifyy',
        razorpay_order_id,
        razorpay_payment_id,
        razorpay_signature,
      );

      const opt = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: '*/*',
          'client-type': 'mobile-app',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          razorpay_order_id,
          razorpay_payment_id,
          razorpay_signature,
          amount,
        }),
      };
      return fetch(url, opt);
    },

    applyForExam: async function (examId: any, applicantId: any) {
      const token = await AsyncStorage.getItem('token');
      const url = `${ExamBaseUrl}examApplication`;
      console.log(examId, applicantId);

      const options = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: '*/*',
          'client-type': 'mobile-app',
          'Server-Select': 'uwhizServer',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({examId, applicantId}),
      };

      return fetch(url, options);
    },
  },
  CategoryData: {
    getCategoryCounts: async function () {
      const Token = await AsyncStorage.getItem('token');
      console.log('Token', Token);

      let url = `${apiUrl}/classes/category-counts`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
            Authorization: `Bearer ${Token}`,
            Cookie: `client_jwt ${Token}`,
          },
        };

      console.log('Fetching category counts...');
      return fetch(url, opt);
    },
  },
  Thought: {
    getThought: async function () {
      const Token = await AsyncStorage.getItem('token');
      console.log('Token', Token);

      let url = `${apiUrl}/classes-thought?status=APPROVED&page=1&limit=5`,
        opt = {
          method: 'GET',
          headers: {
            Accept: '*/*',
            Authorization: `Bearer ${Token}`,
            Cookie: `client_jwt ${Token}`,
          },
        };

      console.log('Fetching thoughts data from api ...');
      return fetch(url, opt);
    },
  },
  ForTutor: {
    getFiveTutor: function (page = 1, limit = 9) {
      let url = `${apiUrl}/classes/approved-tutors?page=${page}&limit=${limit}&sortByRating=true&sortByReviewCount=true`,
        opt = {
          method: 'GET',
          headers: {
            // "client-type":"mobile-app",
            Accept: '*/*',
          },
          // body:JSON.stringify(data)
        };
      console.log('classlist fetch with', limit);

      console.log('IN CLASS LIST API FUNCTION::');

      return fetch(url, opt);
    },
  },
  ReattemptExam:{
    reattemptexam:function (studentId:any,examId:any){
      let url = `${ExamBaseUrl}check-attempt?studentId=${studentId}&examId=${examId}`,
        opt = {
          method: 'GET',
          headers: {
            // "client-type":"mobile-app",
            Accept: '*/*',
          },
          // body:JSON.stringify(data)
        };
      console.log('reattempt api');


      return fetch(url, opt);
    }
  },

  // Notification APIs
  GetStudentNotifications: {
    getStudentNotifications: async function (page = 1, limit = 10) {
      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/notifications/students?page=${page}&limit=${limit}`,
        opt = {
          method: 'GET',
          headers: {
            ...getCommonHeaders(),
            Cookie: token ? `student_jwt=${token}` : '',
          },
        };
      console.log('IN GET STUDENT NOTIFICATIONS API FUNCTION::');
      return fetch(url, opt);
    },
  },

  GetStudentUnreadCount: {
    getStudentUnreadCount: async function () {
      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/notifications/students/count`,
        opt = {
          method: 'GET',
          headers: {
            ...getCommonHeaders(),
            Cookie: token ? `student_jwt=${token}` : '',
          },
        };
      console.log('IN GET STUDENT UNREAD COUNT API FUNCTION::');
      return fetch(url, opt);
    },
  },

  MarkNotificationAsRead: {
    markNotificationAsRead: async function (notificationId: string) {
      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/notifications/students/mark-read/${notificationId}`,
        opt = {
          method: 'POST',
          headers: {
            ...getCommonHeaders(),
            Cookie: token ? `student_jwt=${token}` : '',
          },
        };
      console.log('IN MARK NOTIFICATION AS READ API FUNCTION::');
      return fetch(url, opt);
    },
  },

  MarkAllNotificationsAsRead: {
    markAllNotificationsAsRead: async function () {
      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/notifications/students/mark-all-read`,
        opt = {
          method: 'POST',
          headers: {
            ...getCommonHeaders(),
            Cookie: token ? `student_jwt=${token}` : '',
          },
        };
      console.log('IN MARK ALL NOTIFICATIONS AS READ API FUNCTION::');
      return fetch(url, opt);
    },
  },

  DeleteAllNotifications: {
    deleteAllNotifications: async function () {
      const token = await AsyncStorage.getItem('token');
      let url = `${apiUrl}/notifications/students/delete-all`,
        opt = {
          method: 'DELETE',
          headers: {
            ...getCommonHeaders(),
            Cookie: token ? `student_jwt=${token}` : '',
          },
        };
      console.log('IN DELETE ALL NOTIFICATIONS API FUNCTION::');
      return fetch(url, opt);
    },
  },

};
