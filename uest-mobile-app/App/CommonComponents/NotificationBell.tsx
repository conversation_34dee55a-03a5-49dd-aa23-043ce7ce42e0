import React, {useEffect, useState} from 'react';
import {
  TouchableOpacity,
  View,
  Text,
  StyleSheet,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import {RootState} from '../Redux/store';
import {PrimaryColors} from '../Utils/Constants';
import {getStudentUnreadCount} from '../services/notificationService';

interface NotificationBellProps {
  iconColor?: string;
  iconSize?: number;
  badgeColor?: string;
  style?: any;
}

const NotificationBell: React.FC<NotificationBellProps> = ({
  iconColor = '#FFFFFF',
  iconSize = 26,
  badgeColor = PrimaryColors.PRIMARY,
  style,
}) => {
  const navigation = useNavigation();
  const isDarkMode = useSelector((state: RootState) => state.theme.isDarkMode);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);

  const styles = isDarkMode ? darkStyles : lightStyles;

  const fetchUnreadCount = async () => {
    try {
      setLoading(true);
      const count = await getStudentUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Error fetching unread count:', error);
      setUnreadCount(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUnreadCount();
    
    // Set up interval to refresh unread count every 30 seconds
    const interval = setInterval(fetchUnreadCount, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // Refresh unread count when screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      fetchUnreadCount();
    });

    return unsubscribe;
  }, [navigation]);

  // Expose refresh function globally for triggering after notifications
  useEffect(() => {
    // Store the refresh function globally so other components can call it
    (global as any).refreshNotificationCount = fetchUnreadCount;

    return () => {
      delete (global as any).refreshNotificationCount;
    };
  }, []);

  const handlePress = () => {
    navigation.navigate('Notifications' as never);
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <Ionicons
          name="notifications-outline"
          size={iconSize}
          color={iconColor}
        />
        {unreadCount > 0 && (
          <View style={[styles.badge, {backgroundColor: badgeColor}]}>
            <Text style={styles.badgeText}>
              {unreadCount > 99 ? '99+' : unreadCount.toString()}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const lightStyles = StyleSheet.create({
  container: {
    padding: 8,
  },
  iconContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -6,
    right: -6,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
    borderWidth: 2,
    borderColor: PrimaryColors.WHITE,
  },
  badgeText: {
    color: PrimaryColors.WHITE,
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
});

const darkStyles = StyleSheet.create({
  container: {
    padding: 8,
  },
  iconContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -6,
    right: -6,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
    borderWidth: 2,
    borderColor: PrimaryColors.BLACK,
  },
  badgeText: {
    color: PrimaryColors.WHITE,
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default NotificationBell;
