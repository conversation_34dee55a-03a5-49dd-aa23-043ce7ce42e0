import React, {useEffect, useState} from 'react';
import {
  TouchableOpacity,
  View,
  Text,
  StyleSheet,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import {RootState} from '../Redux/store';
import {PrimaryColors} from '../Utils/Constants';
import {getStudentUnreadCount} from '../services/notificationService';

interface NotificationBellProps {
  iconColor?: string;
  iconSize?: number;
  badgeColor?: string;
  style?: any;
}

const NotificationBell: React.FC<NotificationBellProps> = ({
  iconColor = '#FFFFFF',
  iconSize = 26,
  badgeColor = PrimaryColors.PRIMARY,
  style,
}) => {
  const navigation = useNavigation();
  const isDarkMode = useSelector((state: RootState) => state.theme.isDarkMode);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);

  const styles = isDarkMode ? darkStyles : lightStyles;

  const fetchUnreadCount = async () => {
    try {
      setLoading(true);
      const count = await getStudentUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Error fetching unread count:', error);
      setUnreadCount(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUnreadCount();
    
    // Set up interval to refresh unread count every 30 seconds
    const interval = setInterval(fetchUnreadCount, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // Refresh unread count when screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      fetchUnreadCount();
    });

    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    (global as any).refreshNotificationCount = fetchUnreadCount;
    return () => {
      delete (global as any).refreshNotificationCount;
    };
  }, []);

  const handlePress = () => {
    navigation.navigate('Notifications' as never);
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <Ionicons
          name="notifications-outline"
          size={iconSize}
          color={iconColor}
        />
        {unreadCount > 0 && (
          <View style={[styles.badge, {backgroundColor: badgeColor}]}>
            <Text style={styles.badgeText}>
              {unreadCount > 99 ? '99+' : unreadCount.toString()}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const lightStyles = StyleSheet.create({
  container: {
    padding: 8,
  },
  iconContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
    borderWidth: 2,
    borderColor: PrimaryColors.WHITE,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  badgeText: {
    color: PrimaryColors.WHITE,
    fontSize: 11,
    fontWeight: '700',
    textAlign: 'center',
  },
});

const darkStyles = StyleSheet.create({
  container: {
    padding: 8,
  },
  iconContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
    borderWidth: 2,
    borderColor: '#1E293B',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 8,
  },
  badgeText: {
    color: PrimaryColors.WHITE,
    fontSize: 11,
    fontWeight: '700',
    textAlign: 'center',
  },
});

export default NotificationBell;
