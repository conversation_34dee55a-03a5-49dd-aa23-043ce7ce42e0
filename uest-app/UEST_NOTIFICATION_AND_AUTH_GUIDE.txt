# UEST App - Notification Service & Authentication Guide for React Native

## 📱 NOTIFICATION SERVICE OVERVIEW

### How Notifications Work in UEST App:

1. **Two User Types Supported:**
   - CLASS (Tutors/Teachers)
   - STUDENT (Students)

2. **Notification Types:**
   - Student: ACCOUNT_CREATED, PROFILE_APPROVED/REJECTED, COIN_PURCHASE, UWHIZ_PARTICIPATION, CHAT_MESSAGE
   - Class: ACCOUNT_CREATED, PROFILE_APPROVED/REJECTED, COIN_PURCHASE, CHAT_MESSAGE, CONTENT_APPROVED/REJECTED, EDUCATION_ADDED, EX<PERSON><PERSON>IENCE_ADDED, CERTIFICATE_ADDED
   - Admin: NEW_STUDENT_REGISTRATION, NEW_CLASS_REGISTRATION, PROFILE_REVIEW_REQUIRED, CONTENT_REVIEW_REQUIRED

3. **Notification Features:**
   - Real-time notifications with unread count
   - Mark individual notifications as read
   - Mark all notifications as read
   - Delete all notifications
   - Pagination support (page, limit)
   - Auto-refresh every 30 seconds

## 🔗 NO<PERSON>FICATION APIs

### Base URL: 
- Main API: http://localhost:4005/api/v1
- UWhiz API: http://localhost:4006

### Authentication:
- Bearer Token in Authorization header
- Token stored in localStorage as 'studentToken' or cookies for classes

### CLASS/TUTOR NOTIFICATION APIs:

```javascript
// Get Class Notifications (Paginated)
GET /notifications/classes?page=1&limit=10
Headers: { Authorization: "Bearer <token>" }

// Get Unread Count
GET /notifications/classes/count
Headers: { Authorization: "Bearer <token>" }

// Mark Single Notification as Read
POST /notifications/classes/mark-read/{notificationId}
Headers: { Authorization: "Bearer <token>" }

// Mark All Notifications as Read
POST /notifications/classes/mark-all-read
Headers: { Authorization: "Bearer <token>" }

// Delete All Notifications
DELETE /notifications/classes/delete-all
Headers: { Authorization: "Bearer <token>" }
```

### STUDENT NOTIFICATION APIs:

```javascript
// Get Student Notifications (Paginated)
GET /notifications/students?page=1&limit=10
Headers: { Authorization: "Bearer <token>" }

// Get Unread Count
GET /notifications/students/count
Headers: { Authorization: "Bearer <token>" }

// Mark Single Notification as Read
POST /notifications/students/mark-read/{notificationId}
Headers: { Authorization: "Bearer <token>" }

// Mark All Notifications as Read
POST /notifications/students/mark-all-read
Headers: { Authorization: "Bearer <token>" }

// Delete All Notifications
DELETE /notifications/students/delete-all
Headers: { Authorization: "Bearer <token>" }
```

### Response Format:
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "uuid",
        "userId": "uuid",
        "userType": "STUDENT|CLASS|ADMIN",
        "type": "NOTIFICATION_TYPE",
        "title": "Notification Title",
        "message": "Notification Message",
        "data": { "actionType": "OPEN_CHAT", "redirectUrl": "/chat/123" },
        "isRead": false,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalCount": 50,
      "limit": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```

## 🔐 AUTHENTICATION SYSTEM

### Authentication Flow:
1. **Registration/Login** → Send OTP to mobile
2. **OTP Verification** → Get JWT token
3. **Token Storage** → Store in AsyncStorage (React Native)
4. **Auto-login** → Check token on app start

### AUTH APIs:

#### CLASS/TUTOR AUTHENTICATION:
```javascript
// Register Class
POST /auth-client/register
Body: { firstName, lastName, contactNo, referralCode? }

// Login Class
POST /auth-client/login
Body: { contactNo, email? }

// Verify OTP
POST /auth-client/verify-otp
Body: { contactNo, otp, email?, firstName?, lastName? }

// Resend OTP
POST /auth-client/resend-otp
Body: { contactNo, firstName? }

// Continue with Email
POST /auth-client/continue-with-email
Body: { email }

// Generate JWT
POST /auth-client/generate-jwt
Body: { contact, password }

// Logout
POST /auth-client/logout
Headers: { Authorization: "Bearer <token>" }
```

#### STUDENT AUTHENTICATION:
```javascript
// Register Student
POST /student/register
Body: { firstName, lastName, contactNo, referralCode? }

// Login Student
POST /student/login
Body: { contactNo, email? }

// Verify OTP
POST /student/verify-otp
Body: { contactNo, otp, firstName?, lastName?, email? }

// Resend OTP
POST /student/resend-otp
Body: { contactNo, firstName? }

// Continue with Email
POST /student/continue-with-email
Body: { email }

// Google Auth
POST /student/google-auth
Body: { googleId, email, firstName, lastName, avatar }
```

## 📱 REACT NATIVE IMPLEMENTATION

### 1. Install Dependencies:
```bash
npm install @react-native-async-storage/async-storage
npm install react-native-push-notification
npm install @react-native-firebase/messaging  # For FCM
npm install axios
npm install @reduxjs/toolkit react-redux
```

### 2. AsyncStorage Helper (utils/storage.js):
```javascript
import AsyncStorage from '@react-native-async-storage/async-storage';

export const setToken = async (token) => {
  try {
    await AsyncStorage.setItem('authToken', token);
  } catch (error) {
    console.error('Error storing token:', error);
  }
};

export const getToken = async () => {
  try {
    return await AsyncStorage.getItem('authToken');
  } catch (error) {
    console.error('Error getting token:', error);
    return null;
  }
};

export const removeToken = async () => {
  try {
    await AsyncStorage.removeItem('authToken');
  } catch (error) {
    console.error('Error removing token:', error);
  }
};

export const setUserData = async (userData) => {
  try {
    await AsyncStorage.setItem('userData', JSON.stringify(userData));
  } catch (error) {
    console.error('Error storing user data:', error);
  }
};

export const getUserData = async () => {
  try {
    const userData = await AsyncStorage.getItem('userData');
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error getting user data:', error);
    return null;
  }
};
```

### 3. Axios Configuration (services/api.js):
```javascript
import axios from 'axios';
import { getToken } from '../utils/storage';

const BASE_URL = 'http://your-api-url.com/api/v1';

const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config) => {
    const token = await getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle token expiration
      // Navigate to login screen
    }
    return Promise.reject(error);
  }
);

export default apiClient;

### 4. Authentication Service (services/authService.js):
```javascript
import apiClient from './api';

export const registerUser = async (userData) => {
  try {
    const response = await apiClient.post('/auth-client/register', userData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const loginUser = async (loginData) => {
  try {
    const response = await apiClient.post('/auth-client/login', loginData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const verifyOTP = async (otpData) => {
  try {
    const response = await apiClient.post('/auth-client/verify-otp', otpData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const resendOTP = async (resendData) => {
  try {
    const response = await apiClient.post('/auth-client/resend-otp', resendData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const logoutUser = async () => {
  try {
    const response = await apiClient.post('/auth-client/logout');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Student Auth Services
export const registerStudent = async (userData) => {
  try {
    const response = await apiClient.post('/student/register', userData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const loginStudent = async (loginData) => {
  try {
    const response = await apiClient.post('/student/login', loginData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const verifyStudentOTP = async (otpData) => {
  try {
    const response = await apiClient.post('/student/verify-otp', otpData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};
```

### 5. Notification Service (services/notificationService.js):
```javascript
import apiClient from './api';

// Class/Tutor Notifications
export const getClassNotifications = async (page = 1, limit = 10) => {
  try {
    const response = await apiClient.get(`/notifications/classes?page=${page}&limit=${limit}`);
    return response.data.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const getClassUnreadCount = async () => {
  try {
    const response = await apiClient.get('/notifications/classes/count');
    return response.data.data.count;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const markClassNotificationAsRead = async (notificationId) => {
  try {
    const response = await apiClient.post(`/notifications/classes/mark-read/${notificationId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const markAllClassNotificationsAsRead = async () => {
  try {
    const response = await apiClient.post('/notifications/classes/mark-all-read');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const deleteAllClassNotifications = async () => {
  try {
    const response = await apiClient.delete('/notifications/classes/delete-all');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Student Notifications
export const getStudentNotifications = async (page = 1, limit = 10) => {
  try {
    const response = await apiClient.get(`/notifications/students?page=${page}&limit=${limit}`);
    return response.data.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const getStudentUnreadCount = async () => {
  try {
    const response = await apiClient.get('/notifications/students/count');
    return response.data.data.count;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const markStudentNotificationAsRead = async (notificationId) => {
  try {
    const response = await apiClient.post(`/notifications/students/mark-read/${notificationId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const markAllStudentNotificationsAsRead = async () => {
  try {
    const response = await apiClient.post('/notifications/students/mark-all-read');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export const deleteAllStudentNotifications = async () => {
  try {
    const response = await apiClient.delete('/notifications/students/delete-all');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};
```

### 6. Redux Store Setup (store/index.js):
```javascript
import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import notificationSlice from './slices/notificationSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    notifications: notificationSlice,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

### 7. Auth Redux Slice (store/slices/authSlice.js):
```javascript
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { setToken, removeToken, setUserData, getUserData, getToken } from '../../utils/storage';
import { loginUser, registerUser, verifyOTP, logoutUser } from '../../services/authService';

// Async thunks
export const loginAsync = createAsyncThunk(
  'auth/login',
  async (loginData, { rejectWithValue }) => {
    try {
      const response = await loginUser(loginData);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const registerAsync = createAsyncThunk(
  'auth/register',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await registerUser(userData);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const verifyOTPAsync = createAsyncThunk(
  'auth/verifyOTP',
  async (otpData, { rejectWithValue }) => {
    try {
      const response = await verifyOTP(otpData);
      if (response.success && response.data.token) {
        await setToken(response.data.token);
        await setUserData(response.data.user);
      }
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const logoutAsync = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await logoutUser();
      await removeToken();
      return true;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const checkAuthStatus = createAsyncThunk(
  'auth/checkStatus',
  async (_, { rejectWithValue }) => {
    try {
      const token = await getToken();
      const userData = await getUserData();
      if (token && userData) {
        return { token, user: userData };
      }
      return null;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState: {
    user: null,
    token: null,
    isAuthenticated: false,
    loading: false,
    error: null,
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearAuth: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Login just sends OTP, doesn't authenticate yet
      })
      .addCase(loginAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Register
      .addCase(registerAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Register just sends OTP, doesn't authenticate yet
      })
      .addCase(registerAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Verify OTP
      .addCase(verifyOTPAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyOTPAsync.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload.success) {
          state.user = action.payload.data.user;
          state.token = action.payload.data.token;
          state.isAuthenticated = true;
        }
      })
      .addCase(verifyOTPAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Logout
      .addCase(logoutAsync.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
      })
      // Check Auth Status
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        if (action.payload) {
          state.user = action.payload.user;
          state.token = action.payload.token;
          state.isAuthenticated = true;
        }
      });
  },
});

export const { clearError, clearAuth } = authSlice.actions;
export default authSlice.reducer;
```

### 8. Notification Redux Slice (store/slices/notificationSlice.js):
```javascript
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  getClassNotifications,
  getStudentNotifications,
  getClassUnreadCount,
  getStudentUnreadCount,
  markClassNotificationAsRead,
  markStudentNotificationAsRead,
} from '../../services/notificationService';

export const fetchNotifications = createAsyncThunk(
  'notifications/fetchNotifications',
  async ({ userType, page = 1, limit = 10 }, { rejectWithValue }) => {
    try {
      let response;
      if (userType === 'class') {
        response = await getClassNotifications(page, limit);
      } else {
        response = await getStudentNotifications(page, limit);
      }
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchUnreadCount = createAsyncThunk(
  'notifications/fetchUnreadCount',
  async (userType, { rejectWithValue }) => {
    try {
      let count;
      if (userType === 'class') {
        count = await getClassUnreadCount();
      } else {
        count = await getStudentUnreadCount();
      }
      return count;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const markAsRead = createAsyncThunk(
  'notifications/markAsRead',
  async ({ userType, notificationId }, { rejectWithValue }) => {
    try {
      if (userType === 'class') {
        await markClassNotificationAsRead(notificationId);
      } else {
        await markStudentNotificationAsRead(notificationId);
      }
      return notificationId;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const notificationSlice = createSlice({
  name: 'notifications',
  initialState: {
    notifications: [],
    unreadCount: 0,
    loading: false,
    error: null,
    pagination: null,
  },
  reducers: {
    clearNotifications: (state) => {
      state.notifications = [];
      state.unreadCount = 0;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchNotifications.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.loading = false;
        state.notifications = action.payload.notifications || [];
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(fetchUnreadCount.fulfilled, (state, action) => {
        state.unreadCount = action.payload;
      })
      .addCase(markAsRead.fulfilled, (state, action) => {
        const notificationId = action.payload;
        state.notifications = state.notifications.map(notif =>
          notif.id === notificationId ? { ...notif, isRead: true } : notif
        );
        state.unreadCount = Math.max(0, state.unreadCount - 1);
      });
  },
});

export const { clearNotifications } = notificationSlice.actions;
export default notificationSlice.reducer;
```

### 9. Login Screen Component (screens/LoginScreen.js):
```javascript
import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { loginAsync, registerAsync, verifyOTPAsync } from '../store/slices/authSlice';

const LoginScreen = ({ navigation }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [otp, setOtp] = useState('');
  const [isRegister, setIsRegister] = useState(false);
  const [showOTP, setShowOTP] = useState(false);
  const [userType, setUserType] = useState('student'); // 'student' or 'class'

  const dispatch = useDispatch();
  const { loading, error } = useSelector((state) => state.auth);

  const handleAuth = async () => {
    if (!phoneNumber) {
      Alert.alert('Error', 'Please enter phone number');
      return;
    }

    try {
      let response;
      if (isRegister) {
        if (!firstName || !lastName) {
          Alert.alert('Error', 'Please enter first and last name');
          return;
        }
        response = await dispatch(registerAsync({
          firstName,
          lastName,
          contactNo: phoneNumber,
        })).unwrap();
      } else {
        response = await dispatch(loginAsync({
          contactNo: phoneNumber,
        })).unwrap();
      }

      if (response.success) {
        setShowOTP(true);
        Alert.alert('Success', 'OTP sent to your phone number');
      }
    } catch (error) {
      Alert.alert('Error', error.message || 'Authentication failed');
    }
  };

  const handleVerifyOTP = async () => {
    if (!otp) {
      Alert.alert('Error', 'Please enter OTP');
      return;
    }

    try {
      const response = await dispatch(verifyOTPAsync({
        contactNo: phoneNumber,
        otp,
        firstName: isRegister ? firstName : undefined,
        lastName: isRegister ? lastName : undefined,
      })).unwrap();

      if (response.success) {
        Alert.alert('Success', 'Login successful');
        navigation.replace('Home');
      }
    } catch (error) {
      Alert.alert('Error', error.message || 'OTP verification failed');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        {isRegister ? 'Register' : 'Login'} as {userType === 'student' ? 'Student' : 'Tutor'}
      </Text>

      {/* User Type Toggle */}
      <View style={styles.toggleContainer}>
        <TouchableOpacity
          style={[styles.toggleButton, userType === 'student' && styles.activeToggle]}
          onPress={() => setUserType('student')}
        >
          <Text style={[styles.toggleText, userType === 'student' && styles.activeToggleText]}>
            Student
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.toggleButton, userType === 'class' && styles.activeToggle]}
          onPress={() => setUserType('class')}
        >
          <Text style={[styles.toggleText, userType === 'class' && styles.activeToggleText]}>
            Tutor
          </Text>
        </TouchableOpacity>
      </View>

      {!showOTP ? (
        <>
          {isRegister && (
            <>
              <TextInput
                style={styles.input}
                placeholder="First Name"
                value={firstName}
                onChangeText={setFirstName}
              />
              <TextInput
                style={styles.input}
                placeholder="Last Name"
                value={lastName}
                onChangeText={setLastName}
              />
            </>
          )}
          <TextInput
            style={styles.input}
            placeholder="Phone Number"
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            keyboardType="phone-pad"
          />
          <TouchableOpacity
            style={styles.button}
            onPress={handleAuth}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>
                {isRegister ? 'Register' : 'Send OTP'}
              </Text>
            )}
          </TouchableOpacity>
        </>
      ) : (
        <>
          <TextInput
            style={styles.input}
            placeholder="Enter OTP"
            value={otp}
            onChangeText={setOtp}
            keyboardType="numeric"
          />
          <TouchableOpacity
            style={styles.button}
            onPress={handleVerifyOTP}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Verify OTP</Text>
            )}
          </TouchableOpacity>
        </>
      )}

      <TouchableOpacity
        style={styles.switchButton}
        onPress={() => setIsRegister(!isRegister)}
      >
        <Text style={styles.switchText}>
          {isRegister ? 'Already have an account? Login' : "Don't have an account? Register"}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  toggleContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    backgroundColor: '#e0e0e0',
    borderRadius: 8,
    padding: 4,
  },
  toggleButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeToggle: {
    backgroundColor: '#007bff',
  },
  toggleText: {
    color: '#666',
    fontWeight: '500',
  },
  activeToggleText: {
    color: '#fff',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 15,
    marginBottom: 15,
    borderRadius: 8,
    backgroundColor: '#fff',
    fontSize: 16,
  },
  button: {
    backgroundColor: '#007bff',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 15,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  switchButton: {
    alignItems: 'center',
    marginTop: 20,
  },
  switchText: {
    color: '#007bff',
    fontSize: 14,
  },
});

export default LoginScreen;
```

### 10. Notification Screen Component (screens/NotificationScreen.js):
```javascript
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchNotifications,
  fetchUnreadCount,
  markAsRead,
} from '../store/slices/notificationSlice';

const NotificationScreen = () => {
  const dispatch = useDispatch();
  const { notifications, unreadCount, loading } = useSelector(
    (state) => state.notifications
  );
  const { user } = useSelector((state) => state.auth);
  const [refreshing, setRefreshing] = useState(false);

  // Determine user type based on your app logic
  const userType = user?.role === 'TUTOR' ? 'class' : 'student';

  useEffect(() => {
    loadNotifications();
    loadUnreadCount();

    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      loadUnreadCount();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const loadNotifications = () => {
    dispatch(fetchNotifications({ userType, page: 1, limit: 20 }));
  };

  const loadUnreadCount = () => {
    dispatch(fetchUnreadCount(userType));
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadNotifications();
    await loadUnreadCount();
    setRefreshing(false);
  };

  const handleNotificationPress = async (notification) => {
    if (!notification.isRead) {
      try {
        await dispatch(markAsRead({
          userType,
          notificationId: notification.id,
        })).unwrap();
      } catch (error) {
        Alert.alert('Error', 'Failed to mark notification as read');
      }
    }

    // Handle notification action based on type
    if (notification.data?.actionType === 'OPEN_CHAT' && notification.data?.redirectUrl) {
      // Navigate to chat screen
      // navigation.navigate('Chat', { chatId: notification.data.chatId });
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return `${Math.floor(diffInHours / 24)}d ago`;
    }
  };

  const renderNotification = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.notificationItem,
        !item.isRead && styles.unreadNotification,
      ]}
      onPress={() => handleNotificationPress(item)}
    >
      <View style={styles.notificationContent}>
        <Text style={styles.notificationTitle}>{item.title}</Text>
        <Text style={styles.notificationMessage}>{item.message}</Text>
        <Text style={styles.notificationTime}>
          {formatDate(item.createdAt)}
        </Text>
      </View>
      {!item.isRead && <View style={styles.unreadDot} />}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Notifications</Text>
        {unreadCount > 0 && (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{unreadCount}</Text>
          </View>
        )}
      </View>

      <FlatList
        data={notifications}
        renderItem={renderNotification}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No notifications yet</Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  badge: {
    backgroundColor: '#ff4444',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  notificationItem: {
    backgroundColor: '#fff',
    marginHorizontal: 15,
    marginVertical: 5,
    padding: 15,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  unreadNotification: {
    backgroundColor: '#f0f8ff',
    borderLeftWidth: 4,
    borderLeftColor: '#007bff',
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  notificationMessage: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  notificationTime: {
    fontSize: 12,
    color: '#999',
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#007bff',
    marginLeft: 10,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
  },
});

export default NotificationScreen;
```

### 11. App Setup (App.js):
```javascript
import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Provider, useDispatch, useSelector } from 'react-redux';
import { store } from './store';
import { checkAuthStatus } from './store/slices/authSlice';

import LoginScreen from './screens/LoginScreen';
import HomeScreen from './screens/HomeScreen';
import NotificationScreen from './screens/NotificationScreen';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const dispatch = useDispatch();
  const { isAuthenticated, loading } = useSelector((state) => state.auth);

  useEffect(() => {
    dispatch(checkAuthStatus());
  }, [dispatch]);

  if (loading) {
    return null; // Show splash screen
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {isAuthenticated ? (
          <>
            <Stack.Screen name="Home" component={HomeScreen} />
            <Stack.Screen name="Notifications" component={NotificationScreen} />
          </>
        ) : (
          <Stack.Screen name="Login" component={LoginScreen} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const App = () => {
  return (
    <Provider store={store}>
      <AppNavigator />
    </Provider>
  );
};

export default App;
```

## 🚀 IMPLEMENTATION STEPS

1. **Install Dependencies:**
   ```bash
   npm install @react-native-async-storage/async-storage
   npm install @reduxjs/toolkit react-redux
   npm install axios
   npm install @react-navigation/native @react-navigation/stack
   ```

2. **Setup Project Structure:**
   ```
   src/
   ├── services/
   │   ├── api.js
   │   ├── authService.js
   │   └── notificationService.js
   ├── store/
   │   ├── index.js
   │   └── slices/
   │       ├── authSlice.js
   │       └── notificationSlice.js
   ├── screens/
   │   ├── LoginScreen.js
   │   ├── HomeScreen.js
   │   └── NotificationScreen.js
   ├── utils/
   │   └── storage.js
   └── App.js
   ```

3. **Configure API Base URL:**
   - Update BASE_URL in `services/api.js`
   - Set your server URL (e.g., 'https://your-api.com/api/v1')

4. **Test Authentication Flow:**
   - Register/Login → OTP → Token Storage → Auto-login

5. **Test Notifications:**
   - Fetch notifications → Display → Mark as read → Real-time updates

## 📝 IMPORTANT NOTES

- Replace `http://your-api-url.com` with your actual API URL
- Handle push notifications with Firebase Cloud Messaging (FCM)
- Implement proper error handling and loading states
- Add offline support with AsyncStorage caching
- Test on both iOS and Android devices
- Implement proper navigation based on notification types
- Add biometric authentication for enhanced security

This implementation provides a complete authentication and notification system for your React Native app based on your existing UEST backend APIs.
```
